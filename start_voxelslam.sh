#!/bin/bash

# VoxelSLAM 启动脚本 - 解决libusb库冲突问题
echo "🚀 启动VoxelSLAM Phase 1优化版本..."

# 设置正确的库路径，优先使用系统libusb
export LD_LIBRARY_PATH="/usr/lib/x86_64-linux-gnu:$LD_LIBRARY_PATH"

# 设置ROS环境
cd $(dirname $0)
source devel/setup.bash

echo "✅ Phase 1优化已启用："
echo "   - 高效哈希函数 (FNV-1a)"
echo "   - 对象池内存管理"
echo "   - 批量查找接口"

echo "📊 系统信息："
echo "   - libusb路径: $(ldd devel/lib/voxel_slam/voxelslam | grep libusb)"

# 启动VoxelSLAM
echo "🎯 启动 VoxelSLAM Hesai 配置..."
roslaunch voxel_slam vxlm_hesai.launch 