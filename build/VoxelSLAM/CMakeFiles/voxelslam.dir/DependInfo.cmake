# The set of languages for which implicit dependencies are needed:
set(CMAKE_DEPENDS_LANGUAGES
  "CXX"
  )
# The set of files for implicit dependencies of each language:
set(CMAKE_DEPENDS_CHECK_CXX
  "/home/<USER>/ws_voxel_slam2/src/VoxelSLAM/src/BTC.cpp" "/home/<USER>/ws_voxel_slam2/build/VoxelSLAM/CMakeFiles/voxelslam.dir/src/BTC.cpp.o"
  "/home/<USER>/ws_voxel_slam2/src/VoxelSLAM/src/voxelslam.cpp" "/home/<USER>/ws_voxel_slam2/build/VoxelSLAM/CMakeFiles/voxelslam.dir/src/voxelslam.cpp.o"
  )
set(CMAKE_CXX_COMPILER_ID "GNU")

# Preprocessor definitions for this target.
set(CMAKE_TARGET_DEFINITIONS_CXX
  "DISABLE_LIBUSB_1_0"
  "DISABLE_PCAP"
  "DISABLE_PNG"
  "ROSCONSOLE_BACKEND_LOG4CXX"
  "ROS_BUILD_SHARED_LIBS=1"
  "ROS_PACKAGE_NAME=\"voxel_slam\""
  "qh_QHpointer"
  "vtkRenderingContext2D_AUTOINIT=1(vtkRenderingContextOpenGL2)"
  "vtkRenderingCore_AUTOINIT=3(vtkInteractionStyle,vtkRenderingFreeType,vtkRenderingOpenGL2)"
  )

# The include file search paths:
set(CMAKE_CXX_TARGET_INCLUDE_PATH
  "/home/<USER>/ws_voxel_slam2/src/VoxelSLAM/include"
  "/home/<USER>/ws_voxel_slam2/ws_livox/devel/include"
  "/opt/ros/noetic/include"
  "/opt/ros/noetic/share/xmlrpcpp/cmake/../../../include/xmlrpcpp"
  "/usr/include/vtk-7.1"
  "/usr/include/freetype2"
  "/usr/include/eigen3"
  "/usr/include/pcl-1.10"
  "/usr/include/ni"
  "/usr/include/openni2"
  "/usr/local/include/gtsam/3rdparty/metis"
  "/usr/local/include/gtsam/3rdparty/SuiteSparse_config"
  "/usr/local/include/gtsam/3rdparty/CCOLAMD"
  "/usr/local/include/gtsam/3rdparty/Eigen"
  )

# Targets to which this target links.
set(CMAKE_TARGET_LINKED_INFO_FILES
  )

# Fortran module output directory.
set(CMAKE_Fortran_TARGET_MODULE_DIR "")
