# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.16

# Default target executed when no arguments are given to make.
default_target: all

.PHONY : default_target

# Allow only one "make -f Makefile2" at a time, but pass parallelism.
.NOTPARALLEL:


#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:


# Remove some rules from gmake that .SUFFIXES does not remove.
SUFFIXES =

.SUFFIXES: .hpux_make_needs_suffix_list


# Suppress display of executed commands.
$(VERBOSE).SILENT:


# A target that is always out of date.
cmake_force:

.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E remove -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/ws_voxel_slam2/src

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/ws_voxel_slam2/build

#=============================================================================
# Targets provided globally by CMake.

# Special rule for the target install
install: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Install the project..."
	/usr/bin/cmake -P cmake_install.cmake
.PHONY : install

# Special rule for the target install
install/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Install the project..."
	/usr/bin/cmake -P cmake_install.cmake
.PHONY : install/fast

# Special rule for the target list_install_components
list_install_components:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Available install components are: \"Unspecified\""
.PHONY : list_install_components

# Special rule for the target list_install_components
list_install_components/fast: list_install_components

.PHONY : list_install_components/fast

# Special rule for the target install/strip
install/strip: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Installing the project stripped..."
	/usr/bin/cmake -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
.PHONY : install/strip

# Special rule for the target install/strip
install/strip/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Installing the project stripped..."
	/usr/bin/cmake -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
.PHONY : install/strip/fast

# Special rule for the target install/local
install/local: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Installing only the local directory..."
	/usr/bin/cmake -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
.PHONY : install/local

# Special rule for the target install/local
install/local/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Installing only the local directory..."
	/usr/bin/cmake -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
.PHONY : install/local/fast

# Special rule for the target edit_cache
edit_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "No interactive CMake dialog available..."
	/usr/bin/cmake -E echo No\ interactive\ CMake\ dialog\ available.
.PHONY : edit_cache

# Special rule for the target edit_cache
edit_cache/fast: edit_cache

.PHONY : edit_cache/fast

# Special rule for the target test
test:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Running tests..."
	/usr/bin/ctest --force-new-ctest-process $(ARGS)
.PHONY : test

# Special rule for the target test
test/fast: test

.PHONY : test/fast

# Special rule for the target rebuild_cache
rebuild_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Running CMake to regenerate build system..."
	/usr/bin/cmake -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR)
.PHONY : rebuild_cache

# Special rule for the target rebuild_cache
rebuild_cache/fast: rebuild_cache

.PHONY : rebuild_cache/fast

# The main all target
all: cmake_check_build_system
	cd /home/<USER>/ws_voxel_slam2/build && $(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/ws_voxel_slam2/build/CMakeFiles /home/<USER>/ws_voxel_slam2/build/VoxelSLAM/CMakeFiles/progress.marks
	cd /home/<USER>/ws_voxel_slam2/build && $(MAKE) -f CMakeFiles/Makefile2 VoxelSLAM/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/ws_voxel_slam2/build/CMakeFiles 0
.PHONY : all

# The main clean target
clean:
	cd /home/<USER>/ws_voxel_slam2/build && $(MAKE) -f CMakeFiles/Makefile2 VoxelSLAM/clean
.PHONY : clean

# The main clean target
clean/fast: clean

.PHONY : clean/fast

# Prepare targets for installation.
preinstall: all
	cd /home/<USER>/ws_voxel_slam2/build && $(MAKE) -f CMakeFiles/Makefile2 VoxelSLAM/preinstall
.PHONY : preinstall

# Prepare targets for installation.
preinstall/fast:
	cd /home/<USER>/ws_voxel_slam2/build && $(MAKE) -f CMakeFiles/Makefile2 VoxelSLAM/preinstall
.PHONY : preinstall/fast

# clear depends
depend:
	cd /home/<USER>/ws_voxel_slam2/build && $(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 1
.PHONY : depend

# Convenience name for target.
VoxelSLAM/CMakeFiles/voxelslam.dir/rule:
	cd /home/<USER>/ws_voxel_slam2/build && $(MAKE) -f CMakeFiles/Makefile2 VoxelSLAM/CMakeFiles/voxelslam.dir/rule
.PHONY : VoxelSLAM/CMakeFiles/voxelslam.dir/rule

# Convenience name for target.
voxelslam: VoxelSLAM/CMakeFiles/voxelslam.dir/rule

.PHONY : voxelslam

# fast build rule for target.
voxelslam/fast:
	cd /home/<USER>/ws_voxel_slam2/build && $(MAKE) -f VoxelSLAM/CMakeFiles/voxelslam.dir/build.make VoxelSLAM/CMakeFiles/voxelslam.dir/build
.PHONY : voxelslam/fast

# Convenience name for target.
VoxelSLAM/CMakeFiles/points_concat_filter.dir/rule:
	cd /home/<USER>/ws_voxel_slam2/build && $(MAKE) -f CMakeFiles/Makefile2 VoxelSLAM/CMakeFiles/points_concat_filter.dir/rule
.PHONY : VoxelSLAM/CMakeFiles/points_concat_filter.dir/rule

# Convenience name for target.
points_concat_filter: VoxelSLAM/CMakeFiles/points_concat_filter.dir/rule

.PHONY : points_concat_filter

# fast build rule for target.
points_concat_filter/fast:
	cd /home/<USER>/ws_voxel_slam2/build && $(MAKE) -f VoxelSLAM/CMakeFiles/points_concat_filter.dir/build.make VoxelSLAM/CMakeFiles/points_concat_filter.dir/build
.PHONY : points_concat_filter/fast

# Convenience name for target.
VoxelSLAM/CMakeFiles/livox_ros_driver_generate_messages_py.dir/rule:
	cd /home/<USER>/ws_voxel_slam2/build && $(MAKE) -f CMakeFiles/Makefile2 VoxelSLAM/CMakeFiles/livox_ros_driver_generate_messages_py.dir/rule
.PHONY : VoxelSLAM/CMakeFiles/livox_ros_driver_generate_messages_py.dir/rule

# Convenience name for target.
livox_ros_driver_generate_messages_py: VoxelSLAM/CMakeFiles/livox_ros_driver_generate_messages_py.dir/rule

.PHONY : livox_ros_driver_generate_messages_py

# fast build rule for target.
livox_ros_driver_generate_messages_py/fast:
	cd /home/<USER>/ws_voxel_slam2/build && $(MAKE) -f VoxelSLAM/CMakeFiles/livox_ros_driver_generate_messages_py.dir/build.make VoxelSLAM/CMakeFiles/livox_ros_driver_generate_messages_py.dir/build
.PHONY : livox_ros_driver_generate_messages_py/fast

# Convenience name for target.
VoxelSLAM/CMakeFiles/livox_ros_driver_generate_messages_lisp.dir/rule:
	cd /home/<USER>/ws_voxel_slam2/build && $(MAKE) -f CMakeFiles/Makefile2 VoxelSLAM/CMakeFiles/livox_ros_driver_generate_messages_lisp.dir/rule
.PHONY : VoxelSLAM/CMakeFiles/livox_ros_driver_generate_messages_lisp.dir/rule

# Convenience name for target.
livox_ros_driver_generate_messages_lisp: VoxelSLAM/CMakeFiles/livox_ros_driver_generate_messages_lisp.dir/rule

.PHONY : livox_ros_driver_generate_messages_lisp

# fast build rule for target.
livox_ros_driver_generate_messages_lisp/fast:
	cd /home/<USER>/ws_voxel_slam2/build && $(MAKE) -f VoxelSLAM/CMakeFiles/livox_ros_driver_generate_messages_lisp.dir/build.make VoxelSLAM/CMakeFiles/livox_ros_driver_generate_messages_lisp.dir/build
.PHONY : livox_ros_driver_generate_messages_lisp/fast

# Convenience name for target.
VoxelSLAM/CMakeFiles/livox_ros_driver_generate_messages_eus.dir/rule:
	cd /home/<USER>/ws_voxel_slam2/build && $(MAKE) -f CMakeFiles/Makefile2 VoxelSLAM/CMakeFiles/livox_ros_driver_generate_messages_eus.dir/rule
.PHONY : VoxelSLAM/CMakeFiles/livox_ros_driver_generate_messages_eus.dir/rule

# Convenience name for target.
livox_ros_driver_generate_messages_eus: VoxelSLAM/CMakeFiles/livox_ros_driver_generate_messages_eus.dir/rule

.PHONY : livox_ros_driver_generate_messages_eus

# fast build rule for target.
livox_ros_driver_generate_messages_eus/fast:
	cd /home/<USER>/ws_voxel_slam2/build && $(MAKE) -f VoxelSLAM/CMakeFiles/livox_ros_driver_generate_messages_eus.dir/build.make VoxelSLAM/CMakeFiles/livox_ros_driver_generate_messages_eus.dir/build
.PHONY : livox_ros_driver_generate_messages_eus/fast

# Convenience name for target.
VoxelSLAM/CMakeFiles/nodelet_generate_messages_lisp.dir/rule:
	cd /home/<USER>/ws_voxel_slam2/build && $(MAKE) -f CMakeFiles/Makefile2 VoxelSLAM/CMakeFiles/nodelet_generate_messages_lisp.dir/rule
.PHONY : VoxelSLAM/CMakeFiles/nodelet_generate_messages_lisp.dir/rule

# Convenience name for target.
nodelet_generate_messages_lisp: VoxelSLAM/CMakeFiles/nodelet_generate_messages_lisp.dir/rule

.PHONY : nodelet_generate_messages_lisp

# fast build rule for target.
nodelet_generate_messages_lisp/fast:
	cd /home/<USER>/ws_voxel_slam2/build && $(MAKE) -f VoxelSLAM/CMakeFiles/nodelet_generate_messages_lisp.dir/build.make VoxelSLAM/CMakeFiles/nodelet_generate_messages_lisp.dir/build
.PHONY : nodelet_generate_messages_lisp/fast

# Convenience name for target.
VoxelSLAM/CMakeFiles/nodelet_generate_messages_py.dir/rule:
	cd /home/<USER>/ws_voxel_slam2/build && $(MAKE) -f CMakeFiles/Makefile2 VoxelSLAM/CMakeFiles/nodelet_generate_messages_py.dir/rule
.PHONY : VoxelSLAM/CMakeFiles/nodelet_generate_messages_py.dir/rule

# Convenience name for target.
nodelet_generate_messages_py: VoxelSLAM/CMakeFiles/nodelet_generate_messages_py.dir/rule

.PHONY : nodelet_generate_messages_py

# fast build rule for target.
nodelet_generate_messages_py/fast:
	cd /home/<USER>/ws_voxel_slam2/build && $(MAKE) -f VoxelSLAM/CMakeFiles/nodelet_generate_messages_py.dir/build.make VoxelSLAM/CMakeFiles/nodelet_generate_messages_py.dir/build
.PHONY : nodelet_generate_messages_py/fast

# Convenience name for target.
VoxelSLAM/CMakeFiles/bond_generate_messages_lisp.dir/rule:
	cd /home/<USER>/ws_voxel_slam2/build && $(MAKE) -f CMakeFiles/Makefile2 VoxelSLAM/CMakeFiles/bond_generate_messages_lisp.dir/rule
.PHONY : VoxelSLAM/CMakeFiles/bond_generate_messages_lisp.dir/rule

# Convenience name for target.
bond_generate_messages_lisp: VoxelSLAM/CMakeFiles/bond_generate_messages_lisp.dir/rule

.PHONY : bond_generate_messages_lisp

# fast build rule for target.
bond_generate_messages_lisp/fast:
	cd /home/<USER>/ws_voxel_slam2/build && $(MAKE) -f VoxelSLAM/CMakeFiles/bond_generate_messages_lisp.dir/build.make VoxelSLAM/CMakeFiles/bond_generate_messages_lisp.dir/build
.PHONY : bond_generate_messages_lisp/fast

# Convenience name for target.
VoxelSLAM/CMakeFiles/nodelet_generate_messages_eus.dir/rule:
	cd /home/<USER>/ws_voxel_slam2/build && $(MAKE) -f CMakeFiles/Makefile2 VoxelSLAM/CMakeFiles/nodelet_generate_messages_eus.dir/rule
.PHONY : VoxelSLAM/CMakeFiles/nodelet_generate_messages_eus.dir/rule

# Convenience name for target.
nodelet_generate_messages_eus: VoxelSLAM/CMakeFiles/nodelet_generate_messages_eus.dir/rule

.PHONY : nodelet_generate_messages_eus

# fast build rule for target.
nodelet_generate_messages_eus/fast:
	cd /home/<USER>/ws_voxel_slam2/build && $(MAKE) -f VoxelSLAM/CMakeFiles/nodelet_generate_messages_eus.dir/build.make VoxelSLAM/CMakeFiles/nodelet_generate_messages_eus.dir/build
.PHONY : nodelet_generate_messages_eus/fast

# Convenience name for target.
VoxelSLAM/CMakeFiles/topic_tools_generate_messages_cpp.dir/rule:
	cd /home/<USER>/ws_voxel_slam2/build && $(MAKE) -f CMakeFiles/Makefile2 VoxelSLAM/CMakeFiles/topic_tools_generate_messages_cpp.dir/rule
.PHONY : VoxelSLAM/CMakeFiles/topic_tools_generate_messages_cpp.dir/rule

# Convenience name for target.
topic_tools_generate_messages_cpp: VoxelSLAM/CMakeFiles/topic_tools_generate_messages_cpp.dir/rule

.PHONY : topic_tools_generate_messages_cpp

# fast build rule for target.
topic_tools_generate_messages_cpp/fast:
	cd /home/<USER>/ws_voxel_slam2/build && $(MAKE) -f VoxelSLAM/CMakeFiles/topic_tools_generate_messages_cpp.dir/build.make VoxelSLAM/CMakeFiles/topic_tools_generate_messages_cpp.dir/build
.PHONY : topic_tools_generate_messages_cpp/fast

# Convenience name for target.
VoxelSLAM/CMakeFiles/pcl_ros_gencfg.dir/rule:
	cd /home/<USER>/ws_voxel_slam2/build && $(MAKE) -f CMakeFiles/Makefile2 VoxelSLAM/CMakeFiles/pcl_ros_gencfg.dir/rule
.PHONY : VoxelSLAM/CMakeFiles/pcl_ros_gencfg.dir/rule

# Convenience name for target.
pcl_ros_gencfg: VoxelSLAM/CMakeFiles/pcl_ros_gencfg.dir/rule

.PHONY : pcl_ros_gencfg

# fast build rule for target.
pcl_ros_gencfg/fast:
	cd /home/<USER>/ws_voxel_slam2/build && $(MAKE) -f VoxelSLAM/CMakeFiles/pcl_ros_gencfg.dir/build.make VoxelSLAM/CMakeFiles/pcl_ros_gencfg.dir/build
.PHONY : pcl_ros_gencfg/fast

# Convenience name for target.
VoxelSLAM/CMakeFiles/std_srvs_generate_messages_lisp.dir/rule:
	cd /home/<USER>/ws_voxel_slam2/build && $(MAKE) -f CMakeFiles/Makefile2 VoxelSLAM/CMakeFiles/std_srvs_generate_messages_lisp.dir/rule
.PHONY : VoxelSLAM/CMakeFiles/std_srvs_generate_messages_lisp.dir/rule

# Convenience name for target.
std_srvs_generate_messages_lisp: VoxelSLAM/CMakeFiles/std_srvs_generate_messages_lisp.dir/rule

.PHONY : std_srvs_generate_messages_lisp

# fast build rule for target.
std_srvs_generate_messages_lisp/fast:
	cd /home/<USER>/ws_voxel_slam2/build && $(MAKE) -f VoxelSLAM/CMakeFiles/std_srvs_generate_messages_lisp.dir/build.make VoxelSLAM/CMakeFiles/std_srvs_generate_messages_lisp.dir/build
.PHONY : std_srvs_generate_messages_lisp/fast

# Convenience name for target.
VoxelSLAM/CMakeFiles/dynamic_reconfigure_generate_messages_lisp.dir/rule:
	cd /home/<USER>/ws_voxel_slam2/build && $(MAKE) -f CMakeFiles/Makefile2 VoxelSLAM/CMakeFiles/dynamic_reconfigure_generate_messages_lisp.dir/rule
.PHONY : VoxelSLAM/CMakeFiles/dynamic_reconfigure_generate_messages_lisp.dir/rule

# Convenience name for target.
dynamic_reconfigure_generate_messages_lisp: VoxelSLAM/CMakeFiles/dynamic_reconfigure_generate_messages_lisp.dir/rule

.PHONY : dynamic_reconfigure_generate_messages_lisp

# fast build rule for target.
dynamic_reconfigure_generate_messages_lisp/fast:
	cd /home/<USER>/ws_voxel_slam2/build && $(MAKE) -f VoxelSLAM/CMakeFiles/dynamic_reconfigure_generate_messages_lisp.dir/build.make VoxelSLAM/CMakeFiles/dynamic_reconfigure_generate_messages_lisp.dir/build
.PHONY : dynamic_reconfigure_generate_messages_lisp/fast

# Convenience name for target.
VoxelSLAM/CMakeFiles/topic_tools_generate_messages_py.dir/rule:
	cd /home/<USER>/ws_voxel_slam2/build && $(MAKE) -f CMakeFiles/Makefile2 VoxelSLAM/CMakeFiles/topic_tools_generate_messages_py.dir/rule
.PHONY : VoxelSLAM/CMakeFiles/topic_tools_generate_messages_py.dir/rule

# Convenience name for target.
topic_tools_generate_messages_py: VoxelSLAM/CMakeFiles/topic_tools_generate_messages_py.dir/rule

.PHONY : topic_tools_generate_messages_py

# fast build rule for target.
topic_tools_generate_messages_py/fast:
	cd /home/<USER>/ws_voxel_slam2/build && $(MAKE) -f VoxelSLAM/CMakeFiles/topic_tools_generate_messages_py.dir/build.make VoxelSLAM/CMakeFiles/topic_tools_generate_messages_py.dir/build
.PHONY : topic_tools_generate_messages_py/fast

# Convenience name for target.
VoxelSLAM/CMakeFiles/dynamic_reconfigure_generate_messages_py.dir/rule:
	cd /home/<USER>/ws_voxel_slam2/build && $(MAKE) -f CMakeFiles/Makefile2 VoxelSLAM/CMakeFiles/dynamic_reconfigure_generate_messages_py.dir/rule
.PHONY : VoxelSLAM/CMakeFiles/dynamic_reconfigure_generate_messages_py.dir/rule

# Convenience name for target.
dynamic_reconfigure_generate_messages_py: VoxelSLAM/CMakeFiles/dynamic_reconfigure_generate_messages_py.dir/rule

.PHONY : dynamic_reconfigure_generate_messages_py

# fast build rule for target.
dynamic_reconfigure_generate_messages_py/fast:
	cd /home/<USER>/ws_voxel_slam2/build && $(MAKE) -f VoxelSLAM/CMakeFiles/dynamic_reconfigure_generate_messages_py.dir/build.make VoxelSLAM/CMakeFiles/dynamic_reconfigure_generate_messages_py.dir/build
.PHONY : dynamic_reconfigure_generate_messages_py/fast

# Convenience name for target.
VoxelSLAM/CMakeFiles/dynamic_reconfigure_gencfg.dir/rule:
	cd /home/<USER>/ws_voxel_slam2/build && $(MAKE) -f CMakeFiles/Makefile2 VoxelSLAM/CMakeFiles/dynamic_reconfigure_gencfg.dir/rule
.PHONY : VoxelSLAM/CMakeFiles/dynamic_reconfigure_gencfg.dir/rule

# Convenience name for target.
dynamic_reconfigure_gencfg: VoxelSLAM/CMakeFiles/dynamic_reconfigure_gencfg.dir/rule

.PHONY : dynamic_reconfigure_gencfg

# fast build rule for target.
dynamic_reconfigure_gencfg/fast:
	cd /home/<USER>/ws_voxel_slam2/build && $(MAKE) -f VoxelSLAM/CMakeFiles/dynamic_reconfigure_gencfg.dir/build.make VoxelSLAM/CMakeFiles/dynamic_reconfigure_gencfg.dir/build
.PHONY : dynamic_reconfigure_gencfg/fast

# Convenience name for target.
VoxelSLAM/CMakeFiles/nodelet_generate_messages_cpp.dir/rule:
	cd /home/<USER>/ws_voxel_slam2/build && $(MAKE) -f CMakeFiles/Makefile2 VoxelSLAM/CMakeFiles/nodelet_generate_messages_cpp.dir/rule
.PHONY : VoxelSLAM/CMakeFiles/nodelet_generate_messages_cpp.dir/rule

# Convenience name for target.
nodelet_generate_messages_cpp: VoxelSLAM/CMakeFiles/nodelet_generate_messages_cpp.dir/rule

.PHONY : nodelet_generate_messages_cpp

# fast build rule for target.
nodelet_generate_messages_cpp/fast:
	cd /home/<USER>/ws_voxel_slam2/build && $(MAKE) -f VoxelSLAM/CMakeFiles/nodelet_generate_messages_cpp.dir/build.make VoxelSLAM/CMakeFiles/nodelet_generate_messages_cpp.dir/build
.PHONY : nodelet_generate_messages_cpp/fast

# Convenience name for target.
VoxelSLAM/CMakeFiles/dynamic_reconfigure_generate_messages_eus.dir/rule:
	cd /home/<USER>/ws_voxel_slam2/build && $(MAKE) -f CMakeFiles/Makefile2 VoxelSLAM/CMakeFiles/dynamic_reconfigure_generate_messages_eus.dir/rule
.PHONY : VoxelSLAM/CMakeFiles/dynamic_reconfigure_generate_messages_eus.dir/rule

# Convenience name for target.
dynamic_reconfigure_generate_messages_eus: VoxelSLAM/CMakeFiles/dynamic_reconfigure_generate_messages_eus.dir/rule

.PHONY : dynamic_reconfigure_generate_messages_eus

# fast build rule for target.
dynamic_reconfigure_generate_messages_eus/fast:
	cd /home/<USER>/ws_voxel_slam2/build && $(MAKE) -f VoxelSLAM/CMakeFiles/dynamic_reconfigure_generate_messages_eus.dir/build.make VoxelSLAM/CMakeFiles/dynamic_reconfigure_generate_messages_eus.dir/build
.PHONY : dynamic_reconfigure_generate_messages_eus/fast

# Convenience name for target.
VoxelSLAM/CMakeFiles/std_srvs_generate_messages_cpp.dir/rule:
	cd /home/<USER>/ws_voxel_slam2/build && $(MAKE) -f CMakeFiles/Makefile2 VoxelSLAM/CMakeFiles/std_srvs_generate_messages_cpp.dir/rule
.PHONY : VoxelSLAM/CMakeFiles/std_srvs_generate_messages_cpp.dir/rule

# Convenience name for target.
std_srvs_generate_messages_cpp: VoxelSLAM/CMakeFiles/std_srvs_generate_messages_cpp.dir/rule

.PHONY : std_srvs_generate_messages_cpp

# fast build rule for target.
std_srvs_generate_messages_cpp/fast:
	cd /home/<USER>/ws_voxel_slam2/build && $(MAKE) -f VoxelSLAM/CMakeFiles/std_srvs_generate_messages_cpp.dir/build.make VoxelSLAM/CMakeFiles/std_srvs_generate_messages_cpp.dir/build
.PHONY : std_srvs_generate_messages_cpp/fast

# Convenience name for target.
VoxelSLAM/CMakeFiles/livox_ros_driver_generate_messages_nodejs.dir/rule:
	cd /home/<USER>/ws_voxel_slam2/build && $(MAKE) -f CMakeFiles/Makefile2 VoxelSLAM/CMakeFiles/livox_ros_driver_generate_messages_nodejs.dir/rule
.PHONY : VoxelSLAM/CMakeFiles/livox_ros_driver_generate_messages_nodejs.dir/rule

# Convenience name for target.
livox_ros_driver_generate_messages_nodejs: VoxelSLAM/CMakeFiles/livox_ros_driver_generate_messages_nodejs.dir/rule

.PHONY : livox_ros_driver_generate_messages_nodejs

# fast build rule for target.
livox_ros_driver_generate_messages_nodejs/fast:
	cd /home/<USER>/ws_voxel_slam2/build && $(MAKE) -f VoxelSLAM/CMakeFiles/livox_ros_driver_generate_messages_nodejs.dir/build.make VoxelSLAM/CMakeFiles/livox_ros_driver_generate_messages_nodejs.dir/build
.PHONY : livox_ros_driver_generate_messages_nodejs/fast

# Convenience name for target.
VoxelSLAM/CMakeFiles/bond_generate_messages_cpp.dir/rule:
	cd /home/<USER>/ws_voxel_slam2/build && $(MAKE) -f CMakeFiles/Makefile2 VoxelSLAM/CMakeFiles/bond_generate_messages_cpp.dir/rule
.PHONY : VoxelSLAM/CMakeFiles/bond_generate_messages_cpp.dir/rule

# Convenience name for target.
bond_generate_messages_cpp: VoxelSLAM/CMakeFiles/bond_generate_messages_cpp.dir/rule

.PHONY : bond_generate_messages_cpp

# fast build rule for target.
bond_generate_messages_cpp/fast:
	cd /home/<USER>/ws_voxel_slam2/build && $(MAKE) -f VoxelSLAM/CMakeFiles/bond_generate_messages_cpp.dir/build.make VoxelSLAM/CMakeFiles/bond_generate_messages_cpp.dir/build
.PHONY : bond_generate_messages_cpp/fast

# Convenience name for target.
VoxelSLAM/CMakeFiles/bond_generate_messages_eus.dir/rule:
	cd /home/<USER>/ws_voxel_slam2/build && $(MAKE) -f CMakeFiles/Makefile2 VoxelSLAM/CMakeFiles/bond_generate_messages_eus.dir/rule
.PHONY : VoxelSLAM/CMakeFiles/bond_generate_messages_eus.dir/rule

# Convenience name for target.
bond_generate_messages_eus: VoxelSLAM/CMakeFiles/bond_generate_messages_eus.dir/rule

.PHONY : bond_generate_messages_eus

# fast build rule for target.
bond_generate_messages_eus/fast:
	cd /home/<USER>/ws_voxel_slam2/build && $(MAKE) -f VoxelSLAM/CMakeFiles/bond_generate_messages_eus.dir/build.make VoxelSLAM/CMakeFiles/bond_generate_messages_eus.dir/build
.PHONY : bond_generate_messages_eus/fast

# Convenience name for target.
VoxelSLAM/CMakeFiles/nodelet_generate_messages_nodejs.dir/rule:
	cd /home/<USER>/ws_voxel_slam2/build && $(MAKE) -f CMakeFiles/Makefile2 VoxelSLAM/CMakeFiles/nodelet_generate_messages_nodejs.dir/rule
.PHONY : VoxelSLAM/CMakeFiles/nodelet_generate_messages_nodejs.dir/rule

# Convenience name for target.
nodelet_generate_messages_nodejs: VoxelSLAM/CMakeFiles/nodelet_generate_messages_nodejs.dir/rule

.PHONY : nodelet_generate_messages_nodejs

# fast build rule for target.
nodelet_generate_messages_nodejs/fast:
	cd /home/<USER>/ws_voxel_slam2/build && $(MAKE) -f VoxelSLAM/CMakeFiles/nodelet_generate_messages_nodejs.dir/build.make VoxelSLAM/CMakeFiles/nodelet_generate_messages_nodejs.dir/build
.PHONY : nodelet_generate_messages_nodejs/fast

# Convenience name for target.
VoxelSLAM/CMakeFiles/bond_generate_messages_nodejs.dir/rule:
	cd /home/<USER>/ws_voxel_slam2/build && $(MAKE) -f CMakeFiles/Makefile2 VoxelSLAM/CMakeFiles/bond_generate_messages_nodejs.dir/rule
.PHONY : VoxelSLAM/CMakeFiles/bond_generate_messages_nodejs.dir/rule

# Convenience name for target.
bond_generate_messages_nodejs: VoxelSLAM/CMakeFiles/bond_generate_messages_nodejs.dir/rule

.PHONY : bond_generate_messages_nodejs

# fast build rule for target.
bond_generate_messages_nodejs/fast:
	cd /home/<USER>/ws_voxel_slam2/build && $(MAKE) -f VoxelSLAM/CMakeFiles/bond_generate_messages_nodejs.dir/build.make VoxelSLAM/CMakeFiles/bond_generate_messages_nodejs.dir/build
.PHONY : bond_generate_messages_nodejs/fast

# Convenience name for target.
VoxelSLAM/CMakeFiles/bond_generate_messages_py.dir/rule:
	cd /home/<USER>/ws_voxel_slam2/build && $(MAKE) -f CMakeFiles/Makefile2 VoxelSLAM/CMakeFiles/bond_generate_messages_py.dir/rule
.PHONY : VoxelSLAM/CMakeFiles/bond_generate_messages_py.dir/rule

# Convenience name for target.
bond_generate_messages_py: VoxelSLAM/CMakeFiles/bond_generate_messages_py.dir/rule

.PHONY : bond_generate_messages_py

# fast build rule for target.
bond_generate_messages_py/fast:
	cd /home/<USER>/ws_voxel_slam2/build && $(MAKE) -f VoxelSLAM/CMakeFiles/bond_generate_messages_py.dir/build.make VoxelSLAM/CMakeFiles/bond_generate_messages_py.dir/build
.PHONY : bond_generate_messages_py/fast

# Convenience name for target.
VoxelSLAM/CMakeFiles/topic_tools_generate_messages_lisp.dir/rule:
	cd /home/<USER>/ws_voxel_slam2/build && $(MAKE) -f CMakeFiles/Makefile2 VoxelSLAM/CMakeFiles/topic_tools_generate_messages_lisp.dir/rule
.PHONY : VoxelSLAM/CMakeFiles/topic_tools_generate_messages_lisp.dir/rule

# Convenience name for target.
topic_tools_generate_messages_lisp: VoxelSLAM/CMakeFiles/topic_tools_generate_messages_lisp.dir/rule

.PHONY : topic_tools_generate_messages_lisp

# fast build rule for target.
topic_tools_generate_messages_lisp/fast:
	cd /home/<USER>/ws_voxel_slam2/build && $(MAKE) -f VoxelSLAM/CMakeFiles/topic_tools_generate_messages_lisp.dir/build.make VoxelSLAM/CMakeFiles/topic_tools_generate_messages_lisp.dir/build
.PHONY : topic_tools_generate_messages_lisp/fast

# Convenience name for target.
VoxelSLAM/CMakeFiles/dynamic_reconfigure_generate_messages_nodejs.dir/rule:
	cd /home/<USER>/ws_voxel_slam2/build && $(MAKE) -f CMakeFiles/Makefile2 VoxelSLAM/CMakeFiles/dynamic_reconfigure_generate_messages_nodejs.dir/rule
.PHONY : VoxelSLAM/CMakeFiles/dynamic_reconfigure_generate_messages_nodejs.dir/rule

# Convenience name for target.
dynamic_reconfigure_generate_messages_nodejs: VoxelSLAM/CMakeFiles/dynamic_reconfigure_generate_messages_nodejs.dir/rule

.PHONY : dynamic_reconfigure_generate_messages_nodejs

# fast build rule for target.
dynamic_reconfigure_generate_messages_nodejs/fast:
	cd /home/<USER>/ws_voxel_slam2/build && $(MAKE) -f VoxelSLAM/CMakeFiles/dynamic_reconfigure_generate_messages_nodejs.dir/build.make VoxelSLAM/CMakeFiles/dynamic_reconfigure_generate_messages_nodejs.dir/build
.PHONY : dynamic_reconfigure_generate_messages_nodejs/fast

# Convenience name for target.
VoxelSLAM/CMakeFiles/nodelet_topic_tools_gencfg.dir/rule:
	cd /home/<USER>/ws_voxel_slam2/build && $(MAKE) -f CMakeFiles/Makefile2 VoxelSLAM/CMakeFiles/nodelet_topic_tools_gencfg.dir/rule
.PHONY : VoxelSLAM/CMakeFiles/nodelet_topic_tools_gencfg.dir/rule

# Convenience name for target.
nodelet_topic_tools_gencfg: VoxelSLAM/CMakeFiles/nodelet_topic_tools_gencfg.dir/rule

.PHONY : nodelet_topic_tools_gencfg

# fast build rule for target.
nodelet_topic_tools_gencfg/fast:
	cd /home/<USER>/ws_voxel_slam2/build && $(MAKE) -f VoxelSLAM/CMakeFiles/nodelet_topic_tools_gencfg.dir/build.make VoxelSLAM/CMakeFiles/nodelet_topic_tools_gencfg.dir/build
.PHONY : nodelet_topic_tools_gencfg/fast

# Convenience name for target.
VoxelSLAM/CMakeFiles/dynamic_reconfigure_generate_messages_cpp.dir/rule:
	cd /home/<USER>/ws_voxel_slam2/build && $(MAKE) -f CMakeFiles/Makefile2 VoxelSLAM/CMakeFiles/dynamic_reconfigure_generate_messages_cpp.dir/rule
.PHONY : VoxelSLAM/CMakeFiles/dynamic_reconfigure_generate_messages_cpp.dir/rule

# Convenience name for target.
dynamic_reconfigure_generate_messages_cpp: VoxelSLAM/CMakeFiles/dynamic_reconfigure_generate_messages_cpp.dir/rule

.PHONY : dynamic_reconfigure_generate_messages_cpp

# fast build rule for target.
dynamic_reconfigure_generate_messages_cpp/fast:
	cd /home/<USER>/ws_voxel_slam2/build && $(MAKE) -f VoxelSLAM/CMakeFiles/dynamic_reconfigure_generate_messages_cpp.dir/build.make VoxelSLAM/CMakeFiles/dynamic_reconfigure_generate_messages_cpp.dir/build
.PHONY : dynamic_reconfigure_generate_messages_cpp/fast

# Convenience name for target.
VoxelSLAM/CMakeFiles/topic_tools_generate_messages_eus.dir/rule:
	cd /home/<USER>/ws_voxel_slam2/build && $(MAKE) -f CMakeFiles/Makefile2 VoxelSLAM/CMakeFiles/topic_tools_generate_messages_eus.dir/rule
.PHONY : VoxelSLAM/CMakeFiles/topic_tools_generate_messages_eus.dir/rule

# Convenience name for target.
topic_tools_generate_messages_eus: VoxelSLAM/CMakeFiles/topic_tools_generate_messages_eus.dir/rule

.PHONY : topic_tools_generate_messages_eus

# fast build rule for target.
topic_tools_generate_messages_eus/fast:
	cd /home/<USER>/ws_voxel_slam2/build && $(MAKE) -f VoxelSLAM/CMakeFiles/topic_tools_generate_messages_eus.dir/build.make VoxelSLAM/CMakeFiles/topic_tools_generate_messages_eus.dir/build
.PHONY : topic_tools_generate_messages_eus/fast

# Convenience name for target.
VoxelSLAM/CMakeFiles/std_srvs_generate_messages_eus.dir/rule:
	cd /home/<USER>/ws_voxel_slam2/build && $(MAKE) -f CMakeFiles/Makefile2 VoxelSLAM/CMakeFiles/std_srvs_generate_messages_eus.dir/rule
.PHONY : VoxelSLAM/CMakeFiles/std_srvs_generate_messages_eus.dir/rule

# Convenience name for target.
std_srvs_generate_messages_eus: VoxelSLAM/CMakeFiles/std_srvs_generate_messages_eus.dir/rule

.PHONY : std_srvs_generate_messages_eus

# fast build rule for target.
std_srvs_generate_messages_eus/fast:
	cd /home/<USER>/ws_voxel_slam2/build && $(MAKE) -f VoxelSLAM/CMakeFiles/std_srvs_generate_messages_eus.dir/build.make VoxelSLAM/CMakeFiles/std_srvs_generate_messages_eus.dir/build
.PHONY : std_srvs_generate_messages_eus/fast

# Convenience name for target.
VoxelSLAM/CMakeFiles/std_srvs_generate_messages_nodejs.dir/rule:
	cd /home/<USER>/ws_voxel_slam2/build && $(MAKE) -f CMakeFiles/Makefile2 VoxelSLAM/CMakeFiles/std_srvs_generate_messages_nodejs.dir/rule
.PHONY : VoxelSLAM/CMakeFiles/std_srvs_generate_messages_nodejs.dir/rule

# Convenience name for target.
std_srvs_generate_messages_nodejs: VoxelSLAM/CMakeFiles/std_srvs_generate_messages_nodejs.dir/rule

.PHONY : std_srvs_generate_messages_nodejs

# fast build rule for target.
std_srvs_generate_messages_nodejs/fast:
	cd /home/<USER>/ws_voxel_slam2/build && $(MAKE) -f VoxelSLAM/CMakeFiles/std_srvs_generate_messages_nodejs.dir/build.make VoxelSLAM/CMakeFiles/std_srvs_generate_messages_nodejs.dir/build
.PHONY : std_srvs_generate_messages_nodejs/fast

# Convenience name for target.
VoxelSLAM/CMakeFiles/std_srvs_generate_messages_py.dir/rule:
	cd /home/<USER>/ws_voxel_slam2/build && $(MAKE) -f CMakeFiles/Makefile2 VoxelSLAM/CMakeFiles/std_srvs_generate_messages_py.dir/rule
.PHONY : VoxelSLAM/CMakeFiles/std_srvs_generate_messages_py.dir/rule

# Convenience name for target.
std_srvs_generate_messages_py: VoxelSLAM/CMakeFiles/std_srvs_generate_messages_py.dir/rule

.PHONY : std_srvs_generate_messages_py

# fast build rule for target.
std_srvs_generate_messages_py/fast:
	cd /home/<USER>/ws_voxel_slam2/build && $(MAKE) -f VoxelSLAM/CMakeFiles/std_srvs_generate_messages_py.dir/build.make VoxelSLAM/CMakeFiles/std_srvs_generate_messages_py.dir/build
.PHONY : std_srvs_generate_messages_py/fast

# Convenience name for target.
VoxelSLAM/CMakeFiles/topic_tools_generate_messages_nodejs.dir/rule:
	cd /home/<USER>/ws_voxel_slam2/build && $(MAKE) -f CMakeFiles/Makefile2 VoxelSLAM/CMakeFiles/topic_tools_generate_messages_nodejs.dir/rule
.PHONY : VoxelSLAM/CMakeFiles/topic_tools_generate_messages_nodejs.dir/rule

# Convenience name for target.
topic_tools_generate_messages_nodejs: VoxelSLAM/CMakeFiles/topic_tools_generate_messages_nodejs.dir/rule

.PHONY : topic_tools_generate_messages_nodejs

# fast build rule for target.
topic_tools_generate_messages_nodejs/fast:
	cd /home/<USER>/ws_voxel_slam2/build && $(MAKE) -f VoxelSLAM/CMakeFiles/topic_tools_generate_messages_nodejs.dir/build.make VoxelSLAM/CMakeFiles/topic_tools_generate_messages_nodejs.dir/build
.PHONY : topic_tools_generate_messages_nodejs/fast

# Convenience name for target.
VoxelSLAM/CMakeFiles/livox_ros_driver_generate_messages_cpp.dir/rule:
	cd /home/<USER>/ws_voxel_slam2/build && $(MAKE) -f CMakeFiles/Makefile2 VoxelSLAM/CMakeFiles/livox_ros_driver_generate_messages_cpp.dir/rule
.PHONY : VoxelSLAM/CMakeFiles/livox_ros_driver_generate_messages_cpp.dir/rule

# Convenience name for target.
livox_ros_driver_generate_messages_cpp: VoxelSLAM/CMakeFiles/livox_ros_driver_generate_messages_cpp.dir/rule

.PHONY : livox_ros_driver_generate_messages_cpp

# fast build rule for target.
livox_ros_driver_generate_messages_cpp/fast:
	cd /home/<USER>/ws_voxel_slam2/build && $(MAKE) -f VoxelSLAM/CMakeFiles/livox_ros_driver_generate_messages_cpp.dir/build.make VoxelSLAM/CMakeFiles/livox_ros_driver_generate_messages_cpp.dir/build
.PHONY : livox_ros_driver_generate_messages_cpp/fast

src/BTC.o: src/BTC.cpp.o

.PHONY : src/BTC.o

# target to build an object file
src/BTC.cpp.o:
	cd /home/<USER>/ws_voxel_slam2/build && $(MAKE) -f VoxelSLAM/CMakeFiles/voxelslam.dir/build.make VoxelSLAM/CMakeFiles/voxelslam.dir/src/BTC.cpp.o
.PHONY : src/BTC.cpp.o

src/BTC.i: src/BTC.cpp.i

.PHONY : src/BTC.i

# target to preprocess a source file
src/BTC.cpp.i:
	cd /home/<USER>/ws_voxel_slam2/build && $(MAKE) -f VoxelSLAM/CMakeFiles/voxelslam.dir/build.make VoxelSLAM/CMakeFiles/voxelslam.dir/src/BTC.cpp.i
.PHONY : src/BTC.cpp.i

src/BTC.s: src/BTC.cpp.s

.PHONY : src/BTC.s

# target to generate assembly for a file
src/BTC.cpp.s:
	cd /home/<USER>/ws_voxel_slam2/build && $(MAKE) -f VoxelSLAM/CMakeFiles/voxelslam.dir/build.make VoxelSLAM/CMakeFiles/voxelslam.dir/src/BTC.cpp.s
.PHONY : src/BTC.cpp.s

src/points_concat_filter.o: src/points_concat_filter.cpp.o

.PHONY : src/points_concat_filter.o

# target to build an object file
src/points_concat_filter.cpp.o:
	cd /home/<USER>/ws_voxel_slam2/build && $(MAKE) -f VoxelSLAM/CMakeFiles/points_concat_filter.dir/build.make VoxelSLAM/CMakeFiles/points_concat_filter.dir/src/points_concat_filter.cpp.o
.PHONY : src/points_concat_filter.cpp.o

src/points_concat_filter.i: src/points_concat_filter.cpp.i

.PHONY : src/points_concat_filter.i

# target to preprocess a source file
src/points_concat_filter.cpp.i:
	cd /home/<USER>/ws_voxel_slam2/build && $(MAKE) -f VoxelSLAM/CMakeFiles/points_concat_filter.dir/build.make VoxelSLAM/CMakeFiles/points_concat_filter.dir/src/points_concat_filter.cpp.i
.PHONY : src/points_concat_filter.cpp.i

src/points_concat_filter.s: src/points_concat_filter.cpp.s

.PHONY : src/points_concat_filter.s

# target to generate assembly for a file
src/points_concat_filter.cpp.s:
	cd /home/<USER>/ws_voxel_slam2/build && $(MAKE) -f VoxelSLAM/CMakeFiles/points_concat_filter.dir/build.make VoxelSLAM/CMakeFiles/points_concat_filter.dir/src/points_concat_filter.cpp.s
.PHONY : src/points_concat_filter.cpp.s

src/voxelslam.o: src/voxelslam.cpp.o

.PHONY : src/voxelslam.o

# target to build an object file
src/voxelslam.cpp.o:
	cd /home/<USER>/ws_voxel_slam2/build && $(MAKE) -f VoxelSLAM/CMakeFiles/voxelslam.dir/build.make VoxelSLAM/CMakeFiles/voxelslam.dir/src/voxelslam.cpp.o
.PHONY : src/voxelslam.cpp.o

src/voxelslam.i: src/voxelslam.cpp.i

.PHONY : src/voxelslam.i

# target to preprocess a source file
src/voxelslam.cpp.i:
	cd /home/<USER>/ws_voxel_slam2/build && $(MAKE) -f VoxelSLAM/CMakeFiles/voxelslam.dir/build.make VoxelSLAM/CMakeFiles/voxelslam.dir/src/voxelslam.cpp.i
.PHONY : src/voxelslam.cpp.i

src/voxelslam.s: src/voxelslam.cpp.s

.PHONY : src/voxelslam.s

# target to generate assembly for a file
src/voxelslam.cpp.s:
	cd /home/<USER>/ws_voxel_slam2/build && $(MAKE) -f VoxelSLAM/CMakeFiles/voxelslam.dir/build.make VoxelSLAM/CMakeFiles/voxelslam.dir/src/voxelslam.cpp.s
.PHONY : src/voxelslam.cpp.s

# Help Target
help:
	@echo "The following are some of the valid targets for this Makefile:"
	@echo "... all (the default if no target is provided)"
	@echo "... clean"
	@echo "... depend"
	@echo "... install"
	@echo "... list_install_components"
	@echo "... install/strip"
	@echo "... voxelslam"
	@echo "... points_concat_filter"
	@echo "... livox_ros_driver_generate_messages_py"
	@echo "... livox_ros_driver_generate_messages_lisp"
	@echo "... livox_ros_driver_generate_messages_eus"
	@echo "... nodelet_generate_messages_lisp"
	@echo "... nodelet_generate_messages_py"
	@echo "... bond_generate_messages_lisp"
	@echo "... nodelet_generate_messages_eus"
	@echo "... topic_tools_generate_messages_cpp"
	@echo "... install/local"
	@echo "... pcl_ros_gencfg"
	@echo "... std_srvs_generate_messages_lisp"
	@echo "... dynamic_reconfigure_generate_messages_lisp"
	@echo "... topic_tools_generate_messages_py"
	@echo "... dynamic_reconfigure_generate_messages_py"
	@echo "... dynamic_reconfigure_gencfg"
	@echo "... nodelet_generate_messages_cpp"
	@echo "... dynamic_reconfigure_generate_messages_eus"
	@echo "... std_srvs_generate_messages_cpp"
	@echo "... livox_ros_driver_generate_messages_nodejs"
	@echo "... bond_generate_messages_cpp"
	@echo "... bond_generate_messages_eus"
	@echo "... nodelet_generate_messages_nodejs"
	@echo "... bond_generate_messages_nodejs"
	@echo "... bond_generate_messages_py"
	@echo "... topic_tools_generate_messages_lisp"
	@echo "... dynamic_reconfigure_generate_messages_nodejs"
	@echo "... nodelet_topic_tools_gencfg"
	@echo "... edit_cache"
	@echo "... dynamic_reconfigure_generate_messages_cpp"
	@echo "... topic_tools_generate_messages_eus"
	@echo "... std_srvs_generate_messages_eus"
	@echo "... std_srvs_generate_messages_nodejs"
	@echo "... test"
	@echo "... std_srvs_generate_messages_py"
	@echo "... rebuild_cache"
	@echo "... topic_tools_generate_messages_nodejs"
	@echo "... livox_ros_driver_generate_messages_cpp"
	@echo "... src/BTC.o"
	@echo "... src/BTC.i"
	@echo "... src/BTC.s"
	@echo "... src/points_concat_filter.o"
	@echo "... src/points_concat_filter.i"
	@echo "... src/points_concat_filter.s"
	@echo "... src/voxelslam.o"
	@echo "... src/voxelslam.i"
	@echo "... src/voxelslam.s"
.PHONY : help



#=============================================================================
# Special targets to cleanup operation of make.

# Special rule to run CMake to check the build system integrity.
# No rule that depends on this can have commands that come from listfiles
# because they might be regenerated.
cmake_check_build_system:
	cd /home/<USER>/ws_voxel_slam2/build && $(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 0
.PHONY : cmake_check_build_system

