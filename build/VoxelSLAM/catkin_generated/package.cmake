set(_CATKIN_CURRENT_PACKAGE "voxel_slam")
set(voxel_slam_VERSION "0.0.0")
set(voxel_slam_MAINTAINER "zale <<EMAIL>>")
set(voxel_slam_PACKAGE_FORMAT "2")
set(voxel_slam_BUILD_DEPENDS "roscpp" "rospy" "std_msgs" "geometry_msgs" "nav_msgs" "rosbag" "sensor_msgs" "message_generation" "libpcl-all-dev" "tf" "livox_ros_driver")
set(voxel_slam_BUILD_EXPORT_DEPENDS )
set(voxel_slam_BUILDTOOL_DEPENDS "catkin")
set(voxel_slam_BUILDTOOL_EXPORT_DEPENDS )
set(voxel_slam_EXEC_DEPENDS "roscpp" "rospy" "std_msgs" "geometry_msgs" "nav_msgs" "sensor_msgs" "rosbag" "tf" "message_runtime" "libpcl-all" "livox_ros_driver")
set(voxel_slam_RUN_DEPENDS "roscpp" "rospy" "std_msgs" "geometry_msgs" "nav_msgs" "sensor_msgs" "rosbag" "tf" "message_runtime" "libpcl-all" "livox_ros_driver")
set(voxel_slam_TEST_DEPENDS )
set(voxel_slam_DOC_DEPENDS )
set(voxel_slam_URL_WEBSITE "")
set(voxel_slam_URL_BUGTRACKER "")
set(voxel_slam_URL_REPOSITORY "")
set(voxel_slam_DEPRECATED "")