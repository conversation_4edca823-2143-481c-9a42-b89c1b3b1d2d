<?xml version="1.0"?>
<package format="2">
  <name>voxel_slam</name>
  <version>0.0.0</version>
  <description>The voxel_slam package</description>

  <maintainer email="<EMAIL>">zale</maintainer>

  <license>TODO</license>

  <buildtool_depend>catkin</buildtool_depend>
  <build_depend>roscpp</build_depend>
  <build_depend>rospy</build_depend>
  <build_depend>std_msgs</build_depend>
  <build_depend>geometry_msgs</build_depend>
  <build_depend>nav_msgs</build_depend>
  <build_depend>rosbag</build_depend>
  <build_depend>sensor_msgs</build_depend>
  <build_depend>message_generation</build_depend>
  <build_depend>libpcl-all-dev</build_depend>
  <build_depend>tf</build_depend>
  <build_depend>livox_ros_driver</build_depend>

  <exec_depend>roscpp</exec_depend>
  <exec_depend>rospy</exec_depend>
  <exec_depend>std_msgs</exec_depend>
  <exec_depend>geometry_msgs</exec_depend>
  <exec_depend>nav_msgs</exec_depend>
  <exec_depend>sensor_msgs</exec_depend>
  <exec_depend>rosbag</exec_depend>
  <exec_depend>tf</exec_depend>
  <exec_depend>message_runtime</exec_depend>
  <exec_depend>libpcl-all</exec_depend>
  <exec_depend>livox_ros_driver</exec_depend>

  <!-- The export tag contains other, unspecified, tags -->
  <export>
    <!-- Other tools can request additional information be placed here -->

  </export>
</package>
