# The set of languages for which implicit dependencies are needed:
set(CMAKE_DEPENDS_LANGUAGES
  "CXX"
  )
# The set of files for implicit dependencies of each language:
set(CMAKE_DEPENDS_CHECK_CXX
  "/home/<USER>/ws_voxel_slam2/src/VoxelSLAMPointCloud2/src/voxelslam_pc2.cpp" "/home/<USER>/ws_voxel_slam2/build/VoxelSLAMPointCloud2/CMakeFiles/voxelslam_pointcloud2.dir/src/voxelslam_pc2.cpp.o"
  "/home/<USER>/ws_voxel_slam2/build/VoxelSLAMPointCloud2/voxelslam_pointcloud2_autogen/mocs_compilation.cpp" "/home/<USER>/ws_voxel_slam2/build/VoxelSLAMPointCloud2/CMakeFiles/voxelslam_pointcloud2.dir/voxelslam_pointcloud2_autogen/mocs_compilation.cpp.o"
  )
set(CMAKE_CXX_COMPILER_ID "GNU")

# Preprocessor definitions for this target.
set(CMAKE_TARGET_DEFINITIONS_CXX
  "QT_CORE_LIB"
  "QT_GUI_LIB"
  "QT_NO_DEBUG"
  "QT_WIDGETS_LIB"
  "ROSCONSOLE_BACKEND_LOG4CXX"
  "ROS_BUILD_SHARED_LIBS=1"
  "ROS_PACKAGE_NAME=\"voxelslam_pointcloud2\""
  "voxelslam_pointcloud2_EXPORTS"
  )

# The include file search paths:
set(CMAKE_CXX_TARGET_INCLUDE_PATH
  "VoxelSLAMPointCloud2/voxelslam_pointcloud2_autogen/include"
  "/opt/ros/noetic/include"
  "/opt/ros/noetic/share/xmlrpcpp/cmake/../../../include/xmlrpcpp"
  "/usr/include/eigen3"
  "/usr/include/OGRE/Overlay"
  "/usr/include/OGRE"
  "/usr/include/x86_64-linux-gnu/qt5"
  "/usr/include/x86_64-linux-gnu/qt5/QtWidgets"
  "/usr/include/x86_64-linux-gnu/qt5/QtGui"
  "/usr/include/x86_64-linux-gnu/qt5/QtCore"
  "/usr/lib/x86_64-linux-gnu/qt5/mkspecs/linux-g++"
  )

# Targets to which this target links.
set(CMAKE_TARGET_LINKED_INFO_FILES
  )

# Fortran module output directory.
set(CMAKE_Fortran_TARGET_MODULE_DIR "")
