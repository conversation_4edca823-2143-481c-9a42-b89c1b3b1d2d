{"BUILD_DIR": "/home/<USER>/ws_voxel_slam2/build/VoxelSLAMPointCloud2/voxelslam_pointcloud2_autogen", "CMAKE_BINARY_DIR": "/home/<USER>/ws_voxel_slam2/build", "CMAKE_CURRENT_BINARY_DIR": "/home/<USER>/ws_voxel_slam2/build/VoxelSLAMPointCloud2", "CMAKE_CURRENT_SOURCE_DIR": "/home/<USER>/ws_voxel_slam2/src/VoxelSLAMPointCloud2", "CMAKE_EXECUTABLE": "/usr/bin/cmake", "CMAKE_SOURCE_DIR": "/home/<USER>/ws_voxel_slam2/src", "HEADERS": [["/home/<USER>/ws_voxel_slam2/src/VoxelSLAMPointCloud2/src/voxelslam_pc2.hpp", "Mu", "UVLADIE3JM/moc_voxelslam_pc2.cpp"]], "HEADER_EXTENSIONS": ["h", "hh", "h++", "hm", "hpp", "hxx", "in", "txx"], "INCLUDE_DIR": "/home/<USER>/ws_voxel_slam2/build/VoxelSLAMPointCloud2/voxelslam_pointcloud2_autogen/include", "MOC_COMPILATION_FILE": "/home/<USER>/ws_voxel_slam2/build/VoxelSLAMPointCloud2/voxelslam_pointcloud2_autogen/mocs_compilation.cpp", "MOC_DEFINITIONS": ["QT_CORE_LIB", "QT_GUI_LIB", "QT_NO_DEBUG", "QT_WIDGETS_LIB", "ROSCONSOLE_BACKEND_LOG4CXX", "ROS_BUILD_SHARED_LIBS=1", "ROS_PACKAGE_NAME=\"voxelslam_pointcloud2\"", "voxelslam_pointcloud2_EXPORTS"], "MOC_DEPEND_FILTERS": [["Q_PLUGIN_METADATA", "[\n][ \t]*Q_PLUGIN_METADATA[ \t]*\\([^\\)]*FILE[ \t]*\"([^\"]+)\""]], "MOC_INCLUDES": ["/opt/ros/noetic/include", "/opt/ros/noetic/include/xmlrpcpp", "/usr/include/eigen3", "/usr/include/OGRE/Overlay", "/usr/include/OGRE", "/usr/include/x86_64-linux-gnu/qt5", "/usr/include/x86_64-linux-gnu/qt5/QtWidgets", "/usr/include/x86_64-linux-gnu/qt5/QtGui", "/usr/include/x86_64-linux-gnu/qt5/QtCore", "/usr/lib/x86_64-linux-gnu/qt5/mkspecs/linux-g++", "/usr/include", "/usr/include/c++/9", "/usr/include/x86_64-linux-gnu/c++/9", "/usr/include/c++/9/backward", "/usr/lib/gcc/x86_64-linux-gnu/9/include", "/usr/local/include", "/usr/include/x86_64-linux-gnu"], "MOC_MACRO_NAMES": ["Q_OBJECT", "Q_GADGET", "Q_NAMESPACE"], "MOC_OPTIONS": [], "MOC_PATH_PREFIX": true, "MOC_PREDEFS_CMD": ["/usr/bin/c++", "-dM", "-E", "-c", "/usr/share/cmake-3.16/Modules/CMakeCXXCompilerABI.cpp"], "MOC_PREDEFS_FILE": "/home/<USER>/ws_voxel_slam2/build/VoxelSLAMPointCloud2/voxelslam_pointcloud2_autogen/moc_predefs.h", "MOC_RELAXED_MODE": false, "MOC_SKIP": [], "MULTI_CONFIG": false, "PARALLEL": 16, "PARSE_CACHE_FILE": "/home/<USER>/ws_voxel_slam2/build/VoxelSLAMPointCloud2/CMakeFiles/voxelslam_pointcloud2_autogen.dir/ParseCache.txt", "QT_MOC_EXECUTABLE": "/usr/lib/qt5/bin/moc", "QT_UIC_EXECUTABLE": "", "QT_VERSION_MAJOR": 5, "SETTINGS_FILE": "/home/<USER>/ws_voxel_slam2/build/VoxelSLAMPointCloud2/CMakeFiles/voxelslam_pointcloud2_autogen.dir/AutogenUsed.txt", "SOURCES": [["/home/<USER>/ws_voxel_slam2/src/VoxelSLAMPointCloud2/src/voxelslam_pc2.cpp", "Mu"]], "VERBOSITY": 0}