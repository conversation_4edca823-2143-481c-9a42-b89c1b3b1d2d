#IncludeRegexLine: ^[ 	]*[#%][ 	]*(include|import)[ 	]*[<"]([^">]+)([">])

#IncludeRegexScan: ^.*$

#IncludeRegexComplain: ^$

#IncludeRegexTransform: 

/home/<USER>/ws_voxel_slam2/src/HesaiLidar_General_ROS/src/HesaiLidar_General_SDK/src/tcp_command_client.c
arpa/inet.h
-
errno.h
-
fcntl.h
-
netinet/in.h
-
pthread.h
-
setjmp.h
-
signal.h
-
stdarg.h
-
stdio.h
-
stdlib.h
-
string.h
-
sys/ipc.h
-
sys/msg.h
-
sys/socket.h
-
sys/types.h
-
syslog.h
-
unistd.h
-
linux/sockios.h
-
net/if.h
-
sys/ioctl.h
-
src/util.h
/home/<USER>/ws_voxel_slam2/src/HesaiLidar_General_ROS/src/HesaiLidar_General_SDK/src/src/util.h
src/tcp_command_client.h
/home/<USER>/ws_voxel_slam2/src/HesaiLidar_General_ROS/src/HesaiLidar_General_SDK/src/src/tcp_command_client.h

/home/<USER>/ws_voxel_slam2/src/HesaiLidar_General_ROS/src/HesaiLidar_General_SDK/src/tcp_command_client.h

/home/<USER>/ws_voxel_slam2/src/HesaiLidar_General_ROS/src/HesaiLidar_General_SDK/src/util.c
arpa/inet.h
-
errno.h
-
fcntl.h
-
netinet/in.h
-
pthread.h
-
setjmp.h
-
signal.h
-
stdarg.h
-
stdio.h
-
stdlib.h
-
string.h
-
strings.h
-
sys/ipc.h
-
sys/msg.h
-
sys/socket.h
-
sys/types.h
-
syslog.h
-
unistd.h
-
src/util.h
/home/<USER>/ws_voxel_slam2/src/HesaiLidar_General_ROS/src/HesaiLidar_General_SDK/src/src/util.h

/home/<USER>/ws_voxel_slam2/src/HesaiLidar_General_ROS/src/HesaiLidar_General_SDK/src/util.h

