# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.16

# compile C with /usr/bin/cc
# compile CXX with /usr/bin/c++
C_FLAGS =   -O3 -DNDEBUG -fPIC   -std=c++14

C_DEFINES = -DBOOST_ALL_NO_LIB -DBOOST_ATOMIC_DYN_LINK -DBOOST_THREAD_DYN_LINK -DDISABLE_LIBUSB_1_0 -DDISABLE_PCAP -DDISABLE_PNG -DPandarGeneralSDK_EXPORTS -DROSCONSOLE_BACKEND_LOG4CXX -DROS_BUILD_SHARED_LIBS=1 -DROS_PACKAGE_NAME=\"hesai_lidar\" -Dqh_QHpointer -DvtkRenderingContext2D_AUTOINIT="1(vtkRenderingContextOpenGL2)" -DvtkRenderingCore_AUTOINIT="3(vtkInteractionStyle,vtk<PERSON><PERSON><PERSON><PERSON><PERSON>Type,vtkRenderingOpenGL2)"

C_INCLUDES = -I/home/<USER>/ws_voxel_slam2/devel/include -I/home/<USER>/ws_voxel_slam2/src/HesaiLidar_General_ROS/src/HesaiLidar_General_SDK -I/home/<USER>/ws_voxel_slam2/src/HesaiLidar_General_ROS/src/HesaiLidar_General_SDK/include -I/home/<USER>/ws_voxel_slam2/src/HesaiLidar_General_ROS/src/HesaiLidar_General_SDK/src/PandarGeneralRaw/include -I/opt/ros/noetic/include -I/opt/ros/noetic/share/xmlrpcpp/cmake/../../../include/xmlrpcpp -isystem /usr/include/vtk-7.1 -isystem /usr/include/freetype2 -isystem /usr/include/pcl-1.10 -isystem /usr/include/eigen3 -isystem /usr/include/ni -isystem /usr/include/openni2 

CXX_FLAGS =   -O3 -DNDEBUG -fPIC   -std=c++14

CXX_DEFINES = -DBOOST_ALL_NO_LIB -DBOOST_ATOMIC_DYN_LINK -DBOOST_THREAD_DYN_LINK -DDISABLE_LIBUSB_1_0 -DDISABLE_PCAP -DDISABLE_PNG -DPandarGeneralSDK_EXPORTS -DROSCONSOLE_BACKEND_LOG4CXX -DROS_BUILD_SHARED_LIBS=1 -DROS_PACKAGE_NAME=\"hesai_lidar\" -Dqh_QHpointer -DvtkRenderingContext2D_AUTOINIT="1(vtkRenderingContextOpenGL2)" -DvtkRenderingCore_AUTOINIT="3(vtkInteractionStyle,vtkRenderingFreeType,vtkRenderingOpenGL2)"

CXX_INCLUDES = -I/home/<USER>/ws_voxel_slam2/devel/include -I/home/<USER>/ws_voxel_slam2/src/HesaiLidar_General_ROS/src/HesaiLidar_General_SDK -I/home/<USER>/ws_voxel_slam2/src/HesaiLidar_General_ROS/src/HesaiLidar_General_SDK/include -I/home/<USER>/ws_voxel_slam2/src/HesaiLidar_General_ROS/src/HesaiLidar_General_SDK/src/PandarGeneralRaw/include -I/opt/ros/noetic/include -I/opt/ros/noetic/share/xmlrpcpp/cmake/../../../include/xmlrpcpp -isystem /usr/include/vtk-7.1 -isystem /usr/include/freetype2 -isystem /usr/include/pcl-1.10 -isystem /usr/include/eigen3 -isystem /usr/include/ni -isystem /usr/include/openni2 

