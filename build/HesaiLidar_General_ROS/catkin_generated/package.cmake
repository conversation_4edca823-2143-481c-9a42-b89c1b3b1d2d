set(_CATKIN_CURRENT_PACKAGE "hesai_lidar")
set(hesai_lidar_VERSION "0.2.0")
set(hesai_lidar_MAINTAINER "hesaiwu<PERSON><PERSON>hou <<EMAIL>>")
set(hesai_lidar_PACKAGE_FORMAT "2")
set(hesai_lidar_BUILD_DEPENDS "nodelet" "pluginlib" "roscpp" "roslib" "std_msgs" "sensor_msgs" "message_runtime" "yaml-cpp" "libpcl-all-dev" "image_transport" "pcl_conversions")
set(hesai_lidar_BUILD_EXPORT_DEPENDS "roscpp" "roslib" "std_msgs" "sensor_msgs" "message_runtime" "yaml-cpp" "libpcl-all-dev" "image_transport" "pcl_conversions")
set(hesai_lidar_BUILDTOOL_DEPENDS "catkin")
set(hesai_lidar_BUILDTOOL_EXPORT_DEPENDS )
set(hesai_lidar_EXEC_DEPENDS "nodelet" "pluginlib" "roscpp" "roslib" "std_msgs" "sensor_msgs" "message_runtime" "yaml-cpp" "libpcl-all-dev" "image_transport" "pcl_conversions")
set(hesai_lidar_RUN_DEPENDS "nodelet" "pluginlib" "roscpp" "roslib" "std_msgs" "sensor_msgs" "message_runtime" "yaml-cpp" "libpcl-all-dev" "image_transport" "pcl_conversions")
set(hesai_lidar_TEST_DEPENDS )
set(hesai_lidar_DOC_DEPENDS )
set(hesai_lidar_URL_WEBSITE "")
set(hesai_lidar_URL_BUGTRACKER "")
set(hesai_lidar_URL_REPOSITORY "")
set(hesai_lidar_DEPRECATED "")