<?xml version="1.0"?>
<package format="2">
  <name>hesai_lidar</name>
  <version>0.2.0</version>
  <description>
    The hesai_lidar package includes the driver for the Heisai PandarQT/Pandar64/Pandar40P/Pandar20A/Pandar20B/Pandar40M LiDAR sensor.
  </description>
  <maintainer email="<EMAIL>">hesaiwu<PERSON>ozhou</maintainer>
  <license>Apache 2</license>

  <buildtool_depend>catkin</buildtool_depend>

  <depend>roscpp</depend>
  <depend>roslib</depend>
  <depend>std_msgs</depend>
  <depend>sensor_msgs</depend>
  <depend>message_runtime</depend>
  <depend>yaml-cpp</depend>
  <depend>libpcl-all-dev</depend>
  <depend>image_transport</depend>
  <depend>pcl_conversions</depend>
  <build_depend>nodelet</build_depend>
  <build_depend>pluginlib</build_depend>
  <exec_depend>nodelet</exec_depend>
  <exec_depend>pluginlib</exec_depend>
  <export>
    <nodelet plugin="${prefix}/nodelets.xml"/>
  </export>
</package>
