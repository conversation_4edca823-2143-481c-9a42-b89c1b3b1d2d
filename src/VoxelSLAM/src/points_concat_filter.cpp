// /*
//  * Copyright 2018-2019 Autoware Foundation. All rights reserved.
//  *
//  * Licensed under the Apache License, Version 2.0 (the "License");
//  * you may not use this file except in compliance with the License.
//  * You may obtain a copy of the License at
//  *
//  *     http://www.apache.org/licenses/LICENSE-2.0
//  *
//  * Unless required by applicable law or agreed to in writing, software
//  * distributed under the License is distributed on an "AS IS" BASIS,
//  * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
//  * See the License for the specific language governing permissions and
//  * limitations under the License.
//  */

//  #include <message_filters/subscriber.h>
//  #include <message_filters/sync_policies/approximate_time.h>
//  #include <message_filters/synchronizer.h>
//  #include <pcl/point_types.h>
//  #include <pcl_conversions/pcl_conversions.h>
//  #include <pcl_ros/point_cloud.h>
//  #include <pcl/common/transforms.h>
//  #include <ros/ros.h>
//  #include <sensor_msgs/PointCloud2.h>
//  #include <tf/tf.h>
//  #include <tf/transform_broadcaster.h>
//  #include <Eigen/Core>
//  #include <Eigen/Dense>
 
//  // 自定义点类型
//  struct PointXYZIRT {
//      PCL_ADD_POINT4D;
//      float intensity;
//      uint16_t ring;
//      double timestamp;
//      EIGEN_MAKE_ALIGNED_OPERATOR_NEW
//  } EIGEN_ALIGN16;
 
//  POINT_CLOUD_REGISTER_POINT_STRUCT(PointXYZIRT,
//      (float, x, x)
//      (float, y, y)
//      (float, z, z)
//      (float, intensity, intensity)
//      (uint16_t, ring, ring)
//      (double, timestamp, timestamp)
//  )
 
//  class PointsConcatFilter
//  {
//  public:
//    PointsConcatFilter();
//    ~PointsConcatFilter()
//    {
//      if(cloud_subscriber_top_) delete cloud_subscriber_top_;
//      if(cloud_subscriber_rear_) delete cloud_subscriber_rear_;
//      if(cloud_synchronizer_) delete cloud_synchronizer_;
//    }
 
//  private:
//    typedef PointXYZIRT PointT;
//    typedef pcl::PointCloud<PointT> PointCloudT;
//    typedef sensor_msgs::PointCloud2 PointCloudMsgT;
//    typedef message_filters::sync_policies::ApproximateTime<PointCloudMsgT, PointCloudMsgT> SyncPolicyT;
 
//    ros::NodeHandle node_handle_;
//    message_filters::Subscriber<PointCloudMsgT> *cloud_subscriber_top_;
//    message_filters::Subscriber<PointCloudMsgT> *cloud_subscriber_rear_;
//    message_filters::Synchronizer<SyncPolicyT> *cloud_synchronizer_;
//    ros::Publisher cloud_publisher_;
//    tf::TransformBroadcaster tf_broadcaster_;
 
//    Eigen::Matrix4f transform_matrix_;
 
//    void pointcloud_callback(const PointCloudMsgT::ConstPtr &msg_top, 
//                           const PointCloudMsgT::ConstPtr &msg_rear);
//    void publishTransform();
//    bool convertCloud(const sensor_msgs::PointCloud2ConstPtr& input_msg, 
//                     PointCloudT::Ptr& output_cloud);
//  };
 
//  PointsConcatFilter::PointsConcatFilter() : node_handle_()
//  {
//    // 创建订阅者
//    cloud_subscriber_top_ = new message_filters::Subscriber<PointCloudMsgT>(node_handle_, "/vlp_top/pandar", 10);
//    cloud_subscriber_rear_ = new message_filters::Subscriber<PointCloudMsgT>(node_handle_, "/vlp_rear/pandar", 10);
 
//    // 创建同步器
//    cloud_synchronizer_ = new message_filters::Synchronizer<SyncPolicyT>(
//        SyncPolicyT(10), *cloud_subscriber_top_, *cloud_subscriber_rear_);
   
//    cloud_synchronizer_->registerCallback(
//        boost::bind(&PointsConcatFilter::pointcloud_callback, this, _1, _2));
 
//    // 创建发布者
//    cloud_publisher_ = node_handle_.advertise<PointCloudMsgT>("/vlp_merged/pandar", 1);
 
//    // 初始化变换矩阵 (rear到top)
//    transform_matrix_ << -0.999958, 0.00501354, -0.00765126, -0.000105802,
//                        -0.00890348, -0.341514, 0.939834, 0.201946,
//                        0.00209888, 0.939863, 0.341545, -0.139604,
//                        0, 0, 0, 1;
//  }
 
//  void PointsConcatFilter::publishTransform()
//  {
//    // rear到top的变换矩阵
//    tf::Transform transform;
//    tf::Matrix3x3 rot_matrix(
//      -0.999958, 0.00501354, -0.00765126,
//      -0.00890348, -0.341514, 0.939834,
//      0.00209888, 0.939863, 0.341545
//    );
//    tf::Vector3 translation(-0.000105802, 0.201946, -0.139604);
   
//    transform.setBasis(rot_matrix);
//    transform.setOrigin(translation);
   
//    // 发布静态变换
//    tf_broadcaster_.sendTransform(
//      tf::StampedTransform(transform, ros::Time::now(), "vlp_rear", "vlp_top")
//    );
//  }
 
//  bool PointsConcatFilter::convertCloud(const sensor_msgs::PointCloud2ConstPtr& input_msg, 
//                                       PointCloudT::Ptr& output_cloud)
//  {
//      output_cloud->clear();
//      output_cloud->reserve(input_msg->width * input_msg->height);
 
//      // 获取各字段的偏移量
//      int offset_x = -1, offset_y = -1, offset_z = -1, offset_intensity = -1, offset_ring = -1, offset_timestamp = -1;
//      for (const sensor_msgs::PointField& field : input_msg->fields) {
//          if (field.name == "x") offset_x = field.offset;
//          else if (field.name == "y") offset_y = field.offset;
//          else if (field.name == "z") offset_z = field.offset;
//          else if (field.name == "intensity") offset_intensity = field.offset;
//          else if (field.name == "ring") offset_ring = field.offset;
//          else if (field.name == "timestamp" || field.name == "timestamp") offset_timestamp = field.offset;
//      }
 
//      // 检查必要字段
//      if (offset_x == -1 || offset_y == -1 || offset_z == -1) {
//          ROS_ERROR("Missing required fields (x, y, z) in point cloud");
//          return false;
//      }
 
//      // 遍历所有点
//      const uint8_t* point_ptr = input_msg->data.data();
//      const uint8_t* point_ptr_end = point_ptr + input_msg->data.size();
     
//      while (point_ptr < point_ptr_end) {
//          PointT point;
 
//          // 读取XYZ
//          point.x = *reinterpret_cast<const float*>(point_ptr + offset_x);
//          point.y = *reinterpret_cast<const float*>(point_ptr + offset_y);
//          point.z = *reinterpret_cast<const float*>(point_ptr + offset_z);
 
//          // 读取intensity（如果有）
//          if (offset_intensity != -1) {
//              point.intensity = *reinterpret_cast<const float*>(point_ptr + offset_intensity);
//          } else {
//              point.intensity = 0.0f;
//          }
 
//          // 读取ring（如果有）
//          if (offset_ring != -1) {
//              point.ring = *reinterpret_cast<const uint16_t*>(point_ptr + offset_ring);
//          } else {
//              point.ring = 0;
//          }
 
//          // 读取时间戳
//          if (offset_timestamp != -1) {
//              // 根据字段类型读取时间戳
//              if (input_msg->fields[offset_timestamp].datatype == sensor_msgs::PointField::FLOAT64) {
//                  point.timestamp = *reinterpret_cast<const double*>(point_ptr + offset_timestamp);
//              } else if (input_msg->fields[offset_timestamp].datatype == sensor_msgs::PointField::FLOAT32) {
//                  point.timestamp = static_cast<double>(*reinterpret_cast<const float*>(point_ptr + offset_timestamp));
//              } else {
//                  // 如果时间戳字段类型不支持，使用消息时间戳
//                  point.timestamp = input_msg->header.stamp.toSec();
//              }
//          } else {
//              // 如果没有时间戳字段，使用消息时间戳
//              point.timestamp = input_msg->header.stamp.toSec();
//          }
 
//          // 只添加有效的点
//          if (std::isfinite(point.x) && std::isfinite(point.y) && std::isfinite(point.z)) {
//              output_cloud->points.push_back(point);
//          }
 
//          point_ptr += input_msg->point_step;
//      }
 
//      output_cloud->width = output_cloud->points.size();
//      output_cloud->height = 1;
//      output_cloud->is_dense = false;
//      output_cloud->header.frame_id = input_msg->header.frame_id;
//      output_cloud->header.stamp = input_msg->header.stamp.toNSec() / 1000ull;
 
//      //ROS_INFO("Converted cloud size: %lu points", output_cloud->points.size());
//      return true;
//  }


 
//  void PointsConcatFilter::pointcloud_callback(const PointCloudMsgT::ConstPtr &msg_top,
//      const PointCloudMsgT::ConstPtr &msg_rear)
//  {
//      // 发布tf变换
//      publishTransform();
 
//      try {
//          // 转换点云到PCL格式
//          PointCloudT::Ptr cloud_top(new PointCloudT);
//          PointCloudT::Ptr cloud_rear(new PointCloudT);
//          PointCloudT::Ptr cloud_merged(new PointCloudT);
 
//          if (!convertCloud(msg_top, cloud_top) || !convertCloud(msg_rear, cloud_rear)) {
//              //ROS_ERROR("Failed to convert point cloud!");
//              return;
//          }
 
//          // 打印点云大小
//          //ROS_INFO("Top cloud size: %lu, Rear cloud size: %lu", 
//              cloud_top->points.size(), cloud_rear->points.size();
 
//          // 使用变换矩阵转换rear点云
//          PointCloudT::Ptr cloud_rear_transformed(new PointCloudT);
//          pcl::transformPointCloud(*cloud_rear, *cloud_rear_transformed, transform_matrix_);
 
//          // 为区分来源，设置不同的intensity值，调整rear点云的ring值
//          // 注意：保持原始timestamp不变
//          for(auto& point : cloud_top->points) {
//              //point.intensity = 100.0;  // top点云
//              // top点云的ring值保持不变 (0-31)
//              // timestamp保持原值
//          }
 
//          for(auto& point : cloud_rear_transformed->points) {
//              //point.intensity = 100.0;  // rear点云使用相同的intensity
//              point.ring += 32;  // rear点云的ring值增加32，变成32-63
//              // timestamp保持原值
//          }
 
//          // 创建合并后的点云
//          cloud_merged->reserve(cloud_top->points.size() + cloud_rear_transformed->points.size());
 
//          // 复制点云
//          cloud_merged->points.insert(cloud_merged->points.end(), 
//              cloud_top->points.begin(), 
//              cloud_top->points.end());
//          cloud_merged->points.insert(cloud_merged->points.end(), 
//              cloud_rear_transformed->points.begin(), 
//              cloud_rear_transformed->points.end());
 
//          // 更新合并点云的属性
//          cloud_merged->width = cloud_merged->points.size();
//          cloud_merged->height = 1;
//          cloud_merged->is_dense = false;
//          // 使用top点云的header和时间戳
//          cloud_merged->header = cloud_top->header;
//          cloud_merged->header.stamp = pcl_conversions::toPCL(msg_top->header).stamp;
 
//          // 打印合并后的点云大小
//          //ROS_INFO("Merged cloud size: %lu", cloud_merged->points.size());
 
//          // 转换回ROS消息并发布
//          sensor_msgs::PointCloud2 cloud_merged_msg;
//          pcl::toROSMsg(*cloud_merged, cloud_merged_msg);
//          // 使用top点云的header和时间戳
//          cloud_merged_msg.header = msg_top->header;
//          cloud_merged_msg.header.frame_id = "vlp_top";
//          cloud_publisher_.publish(cloud_merged_msg);
 
//      } catch (const std::exception& e) {
//          //ROS_ERROR("Error in pointcloud_callback: %s", e.what());
//      }
//  }


 
//  int main(int argc, char **argv)
//  {
//    ros::init(argc, argv, "points_concat_filter");
//    PointsConcatFilter node;
//    ros::spin();
//    return 0;
//  }


/*****************************************************************************
 * points_concat_filter.cpp  ——  Top / Rear  XT‑32  点云合并
 * --------------------------------------------------------------------------
 * 1. /vlp_top/pandar            (frame: vlp_top)
 * 2. /vlp_rear/pandar           (frame: vlp_rear)
 *
 * 处理流程
 *   ①   ROS  →  PCL(PointXYZIRT)               （保留原始 timestamp 字段）
 *   ②   rear 点云坐标变换至 vlp_top
 *   ③   “时间归一化”：
 *        · 各自减去首点时间 → 得到 [0,0.1] s 相对时刻
 *        · rear 再加 header 差值  (rear.header – top.header)
 *   ④   仅保留 rear 相对时间 ∈[0,SCAN_PERIOD]，并把 ring+32
 *   ⑤   合并两个点云 → 按 timestamp 排序
 *   ⑥   发布 /vlp_merged/pandar   frame = vlp_top
 *****************************************************************************/

 #include <ros/ros.h>
 #include <sensor_msgs/PointCloud2.h>
 #include <message_filters/subscriber.h>
 #include <message_filters/sync_policies/approximate_time.h>
 #include <message_filters/synchronizer.h>
 
 #include <pcl_ros/point_cloud.h>
 #include <pcl/common/transforms.h>
 #include <pcl_conversions/pcl_conversions.h>
 
 #include <tf/transform_broadcaster.h>
 #include <Eigen/Dense>
 
 /* ----------  自定义点格式：XYZI + ring + timestamp  ---------- */
 struct PointXYZIRT
 {
   PCL_ADD_POINT4D;
   float    intensity;
   uint16_t ring;
   double   timestamp;
   EIGEN_MAKE_ALIGNED_OPERATOR_NEW
 } EIGEN_ALIGN16;
 
 POINT_CLOUD_REGISTER_POINT_STRUCT
   (PointXYZIRT,
    (float,    x,         x)
    (float,    y,         y)
    (float,    z,         z)
    (float,    intensity, intensity)
    (uint16_t, ring,      ring)
    (double,   timestamp, timestamp)
   )
 
 /* ======================================================================== */
 class PointsConcatFilter
 {
   using PointT        = PointXYZIRT;
   using CloudT        = pcl::PointCloud<PointT>;
   using CloudPtr      = CloudT::Ptr;
   using MsgT          = sensor_msgs::PointCloud2;
   using SyncPolicy    = message_filters::sync_policies::ApproximateTime<MsgT,MsgT>;
 
 public:
   PointsConcatFilter();
   ~PointsConcatFilter();
 
 private:
   /* ROS */
   ros::NodeHandle nh_;
   message_filters::Subscriber<MsgT>* sub_top_  = nullptr;
   message_filters::Subscriber<MsgT>* sub_rear_ = nullptr;
   message_filters::Synchronizer<SyncPolicy>* sync_ = nullptr;
   ros::Publisher  pub_;
   mutable tf::TransformBroadcaster tf_pub_;
 
   /* rear→top 外参 */
   Eigen::Matrix4f T_rear2top_;
 
   /* 核心函数 */
   void callback(const MsgT::ConstPtr& msg_top,
                 const MsgT::ConstPtr& msg_rear);
   bool msgToCloud(const MsgT::ConstPtr& msg, CloudPtr& cloud_out) const;
   void publishStaticTF();
 
   /* 参数 */
   static constexpr double SCAN_PERIOD = 0.05;      // XT‑32 单圈时间 (s)
 };
 
 /* =======================  实  现  ============================== */
 PointsConcatFilter::PointsConcatFilter()
 {
   /* --- 订阅 --- */
   sub_top_  = new message_filters::Subscriber<MsgT>(nh_, "/vlp_top/pandar",  10);
   sub_rear_ = new message_filters::Subscriber<MsgT>(nh_, "/vlp_rear/pandar", 10);
 
   sync_ = new message_filters::Synchronizer<SyncPolicy>(SyncPolicy(10),
                                                         *sub_top_,*sub_rear_);
   sync_->registerCallback(&PointsConcatFilter::callback, this);
 
   /* --- 发布 --- */
   pub_ = nh_.advertise<MsgT>("/vlp_merged/pandar", 1);
 
   /* --- rear→top 外参矩阵 --- */
  //  T_rear2top_ << -0.999958, 0.00501354,-0.00765126,-0.000105802,
  //                 -0.00890348,-0.341514, 0.939834 , 0.201946,
  //                  0.00209888, 0.939863, 0.341545 ,-0.139604,
  //                  0,0,0,1;

  T_rear2top_ << -0.999989, -0.00231958, 0.00413785,  0.00259645,
  0.00466633, -0.324158 ,  0.945992  ,  0.180741  ,
  -0.000852989,  0.946 ,  0.324165  , -0.143021  ,
    0        ,  0        ,  0        ,  1;
 }

 
 PointsConcatFilter::~PointsConcatFilter()
 {
   delete sub_top_;
   delete sub_rear_;
   delete sync_;
 }
 
 /* ----------  将 sensor_msgs::PointCloud2 解析为 PCL::PointXYZIRT ---------- */
 bool PointsConcatFilter::msgToCloud(const MsgT::ConstPtr& msg, CloudPtr& out) const
 {
   out->clear(); out->reserve(msg->width * msg->height);
 
   /* 字段偏移获取 */
   int off_x=-1,off_y=-1,off_z=-1,off_int=-1,off_ring=-1,off_ts=-1;
   for(const auto& f: msg->fields){
     if     (f.name=="x")         off_x = f.offset;
     else if(f.name=="y")         off_y = f.offset;
     else if(f.name=="z")         off_z = f.offset;
     else if(f.name=="intensity") off_int= f.offset;
     else if(f.name=="ring")      off_ring=f.offset;
     else if(f.name=="timestamp") off_ts  = f.offset;   // Hesai 字段
   }
   if(off_x<0||off_y<0||off_z<0){
     ROS_ERROR("points_concat_filter: missing xyz field"); return false;
   }
 
   const uint8_t* ptr  = msg->data.data();
   const uint8_t* end  = ptr + msg->data.size();
 
   while(ptr<end){
     PointT p;
     p.x = *reinterpret_cast<const float*>(ptr+off_x);
     p.y = *reinterpret_cast<const float*>(ptr+off_y);
     p.z = *reinterpret_cast<const float*>(ptr+off_z);
     p.intensity = (off_int>=0)? *reinterpret_cast<const float*>(ptr+off_int):0.f;
     p.ring      = (off_ring>=0)? *reinterpret_cast<const uint16_t*>(ptr+off_ring):0;
     if(off_ts>=0){
       /* Hesai timestamp 字段为 double (datatype==8, FLOAT64) */
       if(msg->fields[ std::distance(msg->fields.begin(),
            std::find_if(msg->fields.begin(),msg->fields.end(),
            [&](const sensor_msgs::PointField& f){return f.offset==off_ts;})) ].datatype
            == sensor_msgs::PointField::FLOAT32)
         p.timestamp = static_cast<double>(*reinterpret_cast<const float*>(ptr+off_ts));
       else
         p.timestamp = *reinterpret_cast<const double*>(ptr+off_ts);
     }else{
       p.timestamp = msg->header.stamp.toSec();        // fallback
     }
     if(std::isfinite(p.x)&&std::isfinite(p.y)&&std::isfinite(p.z))
       out->push_back(p);
 
     ptr += msg->point_step;
   }
 
   out->width  = out->size();
   out->height = 1;
   out->is_dense = false;
   out->header   = pcl_conversions::toPCL(msg->header);
 
   return true;
 }
 
 /* ----------  TF (仅方便 RViz，可删除) ---------- */
 void PointsConcatFilter::publishStaticTF() 
 {
   tf::Matrix3x3 rot(-0.999958, 0.00501354,-0.00765126,
                     -0.00890348,-0.341514, 0.939834,
                      0.00209888, 0.939863, 0.341545);
   tf::Transform tf_tr(rot,
                       tf::Vector3(-0.000105802,0.201946,-0.139604));
   tf_pub_.sendTransform(
       tf::StampedTransform(tf_tr, ros::Time::now(), "vlp_rear","vlp_top"));
 }
 
 /* ====================  同步回调  ==================== */
 void PointsConcatFilter::callback(const MsgT::ConstPtr& msg_top,
                                   const MsgT::ConstPtr& msg_rear)
 {
   publishStaticTF();
 
   /* ---------- 1. 转成 PCL ---------- */
   CloudPtr cloud_top (new CloudT);
   CloudPtr cloud_rear(new CloudT);
   if(!msgToCloud(msg_top , cloud_top ) || !msgToCloud(msg_rear, cloud_rear)){
     ROS_WARN("msgToCloud failed"); return;
   }
   if(cloud_top->empty()||cloud_rear->empty()) return;
 
   /* ---------- 2. rear → top 坐标 ---------- */
   CloudPtr cloud_rear_tf(new CloudT);
   pcl::transformPointCloud(*cloud_rear, *cloud_rear_tf, T_rear2top_);
 
   /* ---------- 3. 时间归一化 & 平移 ---------- */
   // (a) 各自减去首点
   const double t0_top  = cloud_top->points.front().timestamp;
   const double t0_rear = cloud_rear_tf->points.front().timestamp;
   for(auto& p: *cloud_top)      p.timestamp -= t0_top;
   for(auto& p: *cloud_rear_tf)  p.timestamp -= t0_rear;
 
   // (b) rear 平移到 top 时基
   const double header_shift =
       msg_rear->header.stamp.toSec() - msg_top->header.stamp.toSec();
 
   CloudT rear_valid; rear_valid.reserve(cloud_rear_tf->size());
 
   for(auto& p: *cloud_rear_tf){
     p.timestamp += header_shift;     // now relative to top frame
     p.ring     += 32;
 
     if(p.timestamp>=0.0 && p.timestamp<=SCAN_PERIOD &&
        std::isfinite(p.x)&&std::isfinite(p.y)&&std::isfinite(p.z))
       rear_valid.push_back(p);
   }
 
   /* ---------- 4. 合并 & 排序 ---------- */
   CloudPtr merged(new CloudT);
   merged->reserve(cloud_top->size() + rear_valid.size());
   merged->insert(merged->end(), cloud_top ->begin(), cloud_top ->end());
   merged->insert(merged->end(), rear_valid.begin(), rear_valid.end());
 
   std::sort(merged->points.begin(), merged->points.end(),
             [](const PointT& a,const PointT& b){ return a.timestamp<b.timestamp; });
 
   merged->width  = merged->size();
   merged->height = 1;
   merged->is_dense = false;
   merged->header   = pcl_conversions::toPCL(msg_top->header);
 
   /* ---------- 5. 发布 ---------- */
   MsgT msg_out;
   pcl::toROSMsg(*merged, msg_out);
   msg_out.header = msg_top->header;          // frame="vlp_top"
   msg_out.header.frame_id = "vlp_top";
   pub_.publish(msg_out);
 }
 
 /* ====================  main  ==================== */
 int main(int argc,char** argv)
 {
   ros::init(argc, argv, "points_concat_filter");
   PointsConcatFilter node;
   ros::spin();
   return 0;
 }
 