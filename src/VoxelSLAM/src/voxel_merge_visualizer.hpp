#ifndef VOXEL_MERGE_VISUALIZER_HPP
#define VOXEL_MERGE_VISUALIZER_HPP

#include <iostream>
#include <fstream>
#include <sstream>
#include <random>
#include <unordered_map>
#include <unordered_set>
#include <tuple>
#include <pcl/io/pcd_io.h>
#include <pcl/point_types.h>
#include <pcl/point_cloud.h>
#include "voxel_map.hpp"
#include "voxel_merge.hpp"
#include <cmath>

using namespace std;

// **关键修复：声明全局变量**
extern double voxel_size;  // 来自voxel_map.hpp的全局体素尺寸

// **体素合并可视化类**
class VoxelMergeVisualizer
{
private:
  VoxelMergeVisualizer() = default;
  
  // **改进的颜色生成器 - 使用HSV颜色空间确保高对比度**
  struct ColorGenerator {
    // HSV转RGB函数
    void hsv_to_rgb(float h, float s, float v, float& r, float& g, float& b) {
      float c = v * s;
      float x = c * (1 - abs(fmod(h / 60.0, 2) - 1));
      float m = v - c;
      
      if (h >= 0 && h < 60) {
        r = c; g = x; b = 0;
      } else if (h >= 60 && h < 120) {
        r = x; g = c; b = 0;
      } else if (h >= 120 && h < 180) {
        r = 0; g = c; b = x;
      } else if (h >= 180 && h < 240) {
        r = 0; g = x; b = c;
      } else if (h >= 240 && h < 300) {
        r = x; g = 0; b = c;
      } else {
        r = c; g = 0; b = x;
      }
      
      r += m; g += m; b += m;
    }
    
    // **为合并组生成高对比度颜色**
    void generate_group_color(int group_id, float& r, float& g, float& b) {
      // 使用黄金比例角度确保颜色均匀分布在色相环上
      float golden_ratio = 0.618033988749895f;
      float hue = fmod(group_id * golden_ratio * 360.0f, 360.0f);
      
      // 高饱和度和亮度，确保颜色鲜明
      float saturation = 0.9f;
      float value = 0.95f;
      
      hsv_to_rgb(hue, saturation, value, r, g, b);
    }
  };

public:
  static VoxelMergeVisualizer& getInstance() {
    static VoxelMergeVisualizer instance;
    return instance;
  }

  // **核心修复：完全重写可视化逻辑，添加平面可视化和高密度边框**
  void save_merge_visualization(
    const unordered_map<VOXEL_LOC, OctoTree*>& surf_map,
    const vector<IMUST>& x_buf,
    int win_base,
    int mgsize,
    const string& save_base_path)
  {
    // **获取VoxelMergeManager实例，用于访问合并组信息**
    auto& merge_manager = VoxelMergeManager::getInstance();
    
    for(int i = 0; i < mgsize; i++) {
      int frame_id = win_base + i;  // **与原始点云完全一致的编号**
      
      // **获取当前帧的位姿，用于坐标系转换**
      const IMUST& current_pose = x_buf[i];  // 当前帧的IMU位姿
      
      // **收集体素并生成可视化点云**
      pcl::PointCloud<pcl::PointXYZRGB> merged_cloud;
      pcl::PointCloud<pcl::PointXYZRGB> unmerged_cloud;
      
      // 统计信息
      int merged_count = 0;
      int unmerged_count = 0;
      int active_merge_groups = 0;
      unordered_set<int> unique_groups;
      
      // **新增：个体平面统计**
      int unmerged_with_planes = 0;  // 有平面的未合并体素
      int unmerged_in_ba = 0;        // 参与BA优化的未合并体素
      int unmerged_no_planes = 0;    // 无平面的未合并体素
      
      // **🚀 新增：按合并组收集体素，为每个组生成统一边界框**
      unordered_map<int, vector<pair<VOXEL_LOC, OctoTree*>>> merge_groups;
      vector<pair<VOXEL_LOC, OctoTree*>> unmerged_voxels;

      // 第一步：按组分类体素
      for(const auto& pair : surf_map) {
        const VOXEL_LOC& loc = pair.first;
        OctoTree* voxel = pair.second;

        // **只处理叶子节点且存在的体素**
        if(!voxel || voxel->octo_state != 0 || !voxel->isexist) continue;

        bool is_merged = (voxel->is_merged && voxel->merge_group_id > 0);

        if(is_merged) {
          merge_groups[voxel->merge_group_id].push_back({loc, voxel});
          unique_groups.insert(voxel->merge_group_id);
        } else {
          unmerged_voxels.push_back({loc, voxel});
        }
      }

      // **第二步：为每个合并组生成统一的边界框和可视化**
      for(const auto& group_pair : merge_groups) {
        int group_id = group_pair.first;
        const auto& group_voxels = group_pair.second;

        if(group_voxels.empty()) continue;

        // **计算组的统一边界框**
        Eigen::Vector3d min_bound(DBL_MAX, DBL_MAX, DBL_MAX);
        Eigen::Vector3d max_bound(-DBL_MAX, -DBL_MAX, -DBL_MAX);

        vector<Eigen::Vector3d> voxel_centers_imu;
        vector<double> voxel_resolutions;

        for(const auto& voxel_pair : group_voxels) {
          const VOXEL_LOC& loc = voxel_pair.first;
          OctoTree* voxel = voxel_pair.second;

          double voxel_resolution = voxel_size / (1 << voxel->layer);
          Eigen::Vector3d voxel_center_world;
          voxel_center_world.x() = (loc.x + 0.5) * voxel_resolution;
          voxel_center_world.y() = (loc.y + 0.5) * voxel_resolution;
          voxel_center_world.z() = (loc.z + 0.5) * voxel_resolution;

          // 转换到IMU坐标系
          Eigen::Vector3d voxel_center_imu = current_pose.R.transpose() * (voxel_center_world - current_pose.p);
          voxel_centers_imu.push_back(voxel_center_imu);
          voxel_resolutions.push_back(voxel_resolution);

          // 更新边界框
          double half_size = voxel_resolution * 0.5;
          min_bound.x() = std::min(min_bound.x(), voxel_center_imu.x() - half_size);
          min_bound.y() = std::min(min_bound.y(), voxel_center_imu.y() - half_size);
          min_bound.z() = std::min(min_bound.z(), voxel_center_imu.z() - half_size);
          max_bound.x() = std::max(max_bound.x(), voxel_center_imu.x() + half_size);
          max_bound.y() = std::max(max_bound.y(), voxel_center_imu.y() + half_size);
          max_bound.z() = std::max(max_bound.z(), voxel_center_imu.z() + half_size);
        }

        // **生成组的颜色**
        ColorGenerator color_gen;
        float r, g, b;
        color_gen.generate_group_color(group_id, r, g, b);

        // **生成统一的边界框**
        vector<pcl::PointXYZRGB> group_wireframe_points;
        generate_group_bounding_box(min_bound, max_bound, group_wireframe_points);

        // **为组内每个体素生成平面可视化**
        for(size_t i = 0; i < group_voxels.size(); i++) {
          OctoTree* voxel = group_voxels[i].second;
          vector<pcl::PointXYZRGB> plane_points;
          generate_merged_plane_visualization(voxel, voxel_centers_imu[i], voxel_resolutions[i],
                                            current_pose, plane_points);

          // 平面用稍微暗一点的颜色
          for(auto& point : plane_points) {
            point.r = static_cast<uint8_t>(r * 200);
            point.g = static_cast<uint8_t>(g * 200);
            point.b = static_cast<uint8_t>(b * 200);
            merged_cloud.push_back(point);
          }
        }

        // **统一着色边界框并添加到点云**
        for(auto& point : group_wireframe_points) {
          point.r = static_cast<uint8_t>(r * 255);
          point.g = static_cast<uint8_t>(g * 255);
          point.b = static_cast<uint8_t>(b * 255);
          merged_cloud.push_back(point);
        }

        merged_count += group_voxels.size();
      }

      // **第三步：处理未合并的体素（保持原有逻辑）**
      for(const auto& voxel_pair : unmerged_voxels) {
        const VOXEL_LOC& loc = voxel_pair.first;
        OctoTree* voxel = voxel_pair.second;

        // **坐标系转换**
        double voxel_resolution = voxel_size / (1 << voxel->layer);
        Eigen::Vector3d voxel_center_world;
        voxel_center_world.x() = (loc.x + 0.5) * voxel_resolution;
        voxel_center_world.y() = (loc.y + 0.5) * voxel_resolution;
        voxel_center_world.z() = (loc.z + 0.5) * voxel_resolution;

        // 转换到IMU坐标系
        Eigen::Vector3d voxel_center_imu = current_pose.R.transpose() * (voxel_center_world - current_pose.p);

        // **未合并体素：生成灰色高密度边框 + 个体平面可视化**
        vector<pcl::PointXYZRGB> wireframe_points;
        generate_high_density_wireframe(voxel_center_imu, voxel_resolution, wireframe_points);

        // **新增：为未合并体素生成个体平面可视化**
        vector<pcl::PointXYZRGB> individual_plane_points;
        generate_individual_plane_visualization(voxel, voxel_center_imu, voxel_resolution,
                                               current_pose, individual_plane_points);

        // 边框：灰色
        for(auto& point : wireframe_points) {
          point.r = point.g = point.b = 128;  // 灰色
          unmerged_cloud.push_back(point);
        }

        // **个体平面：用淡蓝色区分**
        for(auto& point : individual_plane_points) {
          point.r = 100;  // 淡蓝色：低红色
          point.g = 150;  // 中等绿色
          point.b = 200;  // 高蓝色
          unmerged_cloud.push_back(point);
        }

        unmerged_count++;

        // **新增：个体平面统计**
        if(voxel->plane.is_plane) {
          unmerged_with_planes++;
          if(voxel->layer >= 0 && voxel->isexist) {
            unmerged_in_ba++;
          }
        } else {
          unmerged_no_planes++;
        }
      }
      
      active_merge_groups = unique_groups.size();
      
      // **保存文件和统计信息**
      if(!merged_cloud.empty()) {
        string merged_filename = save_base_path + "/merged_" + to_string(frame_id) + ".pcd";
        pcl::io::savePCDFileBinary(merged_filename, merged_cloud);
      }
      
      if(!unmerged_cloud.empty()) {
        string unmerged_filename = save_base_path + "/unmerged_" + to_string(frame_id) + ".pcd";
        pcl::io::savePCDFileBinary(unmerged_filename, unmerged_cloud);
      }
      
      // **保存统计信息**
      string stats_filename = save_base_path + "/stats_" + to_string(frame_id) + ".txt";
      ofstream stats_file(stats_filename);
      if(stats_file.is_open()) {
        stats_file << "Enhanced Voxel Merge Visualization Statistics\n";
        stats_file << "============================================\n";
        stats_file << "Frame ID: " << frame_id << "\n";
        stats_file << "Total voxels processed: " << (merged_count + unmerged_count) << "\n";
        stats_file << "\n--- Merged Voxels ---\n";
        stats_file << "Merged voxels: " << merged_count << "\n";
        stats_file << "Active merge groups: " << active_merge_groups << "\n";
        stats_file << "\n--- Unmerged Voxels ---\n";
        stats_file << "Total unmerged voxels: " << unmerged_count << "\n";
        stats_file << "  - With valid planes: " << unmerged_with_planes << "\n";
        stats_file << "  - Participating in BA: " << unmerged_in_ba << "\n";
        stats_file << "  - Without planes: " << unmerged_no_planes << "\n";
        stats_file << "\n--- BA Optimization Status ---\n";
        stats_file << "Total voxels in BA: " << (merged_count + unmerged_in_ba) << "\n";
        stats_file << "Merged groups contribution: " << merged_count << "\n";
        stats_file << "Individual voxel contribution: " << unmerged_in_ba << "\n";
        stats_file << "\nVisualization Features:\n";
        stats_file << "- High-contrast HSV colors for merged groups\n";
        stats_file << "- 🚀 NEW: Unified bounding boxes for merged groups (instead of individual voxel boxes)\n";
        stats_file << "- Gray wireframes for unmerged voxels\n";
        stats_file << "- Bright plane grids (8x8) for merged voxels\n";
        stats_file << "- Light blue plane grids (6x6) for unmerged voxels\n";
        stats_file << "- Cross markers for individual plane centers\n";
        stats_file << "- High-density wireframe edges (15 points per edge for group boxes, 20 for individual)\n";
        stats_file << "- Enhanced corner visualization for group bounding boxes\n";
        stats_file << "- IMU coordinate system (same as original point clouds)\n";
        stats_file.close();
      }
      
      // **调试输出**
      cout << ": 🚀 Unified Bounding Box Visualization - " << merged_count << " merged + "
           << unmerged_count << " unmerged voxels (" << active_merge_groups << " unified groups)" << endl;
      cout << "  BA participants: " << (merged_count + unmerged_in_ba) << " total ("
           << merged_count << " merged + " << unmerged_in_ba << " individual)" << endl;
      cout << "  Visualization: " << active_merge_groups << " group bounding boxes + "
           << unmerged_count << " individual voxel boxes" << endl;
    }
  }

private:
  // **🚀 新增：生成合并组的统一边界框**
  void generate_group_bounding_box(const Eigen::Vector3d& min_bound, const Eigen::Vector3d& max_bound,
                                  vector<pcl::PointXYZRGB>& points) {
    points.clear();

    // **计算边界框的8个顶点**
    vector<Eigen::Vector3d> vertices = {
      {min_bound.x(), min_bound.y(), min_bound.z()},  // 0: min corner
      {max_bound.x(), min_bound.y(), min_bound.z()},  // 1
      {max_bound.x(), max_bound.y(), min_bound.z()},  // 2
      {min_bound.x(), max_bound.y(), min_bound.z()},  // 3
      {min_bound.x(), min_bound.y(), max_bound.z()},  // 4
      {max_bound.x(), min_bound.y(), max_bound.z()},  // 5
      {max_bound.x(), max_bound.y(), max_bound.z()},  // 6: max corner
      {min_bound.x(), max_bound.y(), max_bound.z()}   // 7
    };

    // **定义边界框的12条边**
    vector<pair<int, int>> edges = {
      // 底面的4条边
      {0, 1}, {1, 2}, {2, 3}, {3, 0},
      // 顶面的4条边
      {4, 5}, {5, 6}, {6, 7}, {7, 4},
      // 连接底面和顶面的4条边
      {0, 4}, {1, 5}, {2, 6}, {3, 7}
    };

    // **为每条边生成高密度点**
    int points_per_edge = 15;  // 每条边15个点，确保边界框清晰可见

    for(const auto& edge : edges) {
      const Eigen::Vector3d& start = vertices[edge.first];
      const Eigen::Vector3d& end = vertices[edge.second];

      for(int i = 0; i <= points_per_edge; i++) {
        double t = static_cast<double>(i) / points_per_edge;
        Eigen::Vector3d point = start + t * (end - start);

        pcl::PointXYZRGB p;
        p.x = point.x();
        p.y = point.y();
        p.z = point.z();
        points.push_back(p);
      }
    }

    // **添加8个顶点，突出显示角点**
    for(const auto& vertex : vertices) {
      pcl::PointXYZRGB p;
      p.x = vertex.x();
      p.y = vertex.y();
      p.z = vertex.z();
      points.push_back(p);

      // **为角点添加额外的强调点**
      for(int dx = -1; dx <= 1; dx++) {
        for(int dy = -1; dy <= 1; dy++) {
          for(int dz = -1; dz <= 1; dz++) {
            if(dx == 0 && dy == 0 && dz == 0) continue;

            pcl::PointXYZRGB corner_point;
            corner_point.x = vertex.x() + dx * 0.01;  // 1cm偏移
            corner_point.y = vertex.y() + dy * 0.01;
            corner_point.z = vertex.z() + dz * 0.01;
            points.push_back(corner_point);
          }
        }
      }
    }
  }

  // **生成高密度体素线框（更清晰的边界）**
  void generate_high_density_wireframe(const Eigen::Vector3d& center, double size,
                                      vector<pcl::PointXYZRGB>& points) {
    double half_size = size * 0.5;
    
    // **生成立方体的8个顶点**
    vector<Eigen::Vector3d> vertices = {
      center + Eigen::Vector3d(-half_size, -half_size, -half_size),  // 0
      center + Eigen::Vector3d( half_size, -half_size, -half_size),  // 1
      center + Eigen::Vector3d( half_size,  half_size, -half_size),  // 2
      center + Eigen::Vector3d(-half_size,  half_size, -half_size),  // 3
      center + Eigen::Vector3d(-half_size, -half_size,  half_size),  // 4
      center + Eigen::Vector3d( half_size, -half_size,  half_size),  // 5
      center + Eigen::Vector3d( half_size,  half_size,  half_size),  // 6
      center + Eigen::Vector3d(-half_size,  half_size,  half_size)   // 7
    };
    
    // **定义立方体的12条边**
    vector<pair<int, int>> edges = {
      {0, 1}, {1, 2}, {2, 3}, {3, 0},  // 底面
      {4, 5}, {5, 6}, {6, 7}, {7, 4},  // 顶面
      {0, 4}, {1, 5}, {2, 6}, {3, 7}   // 竖直边
    };
    
    points.clear();
    
    // **高密度边框：每条边生成更多点**
    int points_per_edge = 10;  // 增加到20个点每条边
    for(const auto& edge : edges) {
      const Eigen::Vector3d& start = vertices[edge.first];
      const Eigen::Vector3d& end = vertices[edge.second];
      
      for(int i = 0; i <= points_per_edge; i++) {
        double t = static_cast<double>(i) / points_per_edge;
        Eigen::Vector3d point = (1.0 - t) * start + t * end;
        
        pcl::PointXYZRGB p;
        p.x = point.x();
        p.y = point.y();
        p.z = point.z();
        points.push_back(p);
      }
    }
    
    // **添加8个顶点，突出显示角点**
    for(const auto& vertex : vertices) {
      pcl::PointXYZRGB p;
      p.x = vertex.x();
      p.y = vertex.y();
      p.z = vertex.z();
      points.push_back(p);
    }
  }
  
  // **生成合并平面的可视化（体素内部的拟合平面）**
  void generate_merged_plane_visualization(OctoTree* voxel, 
                                         const Eigen::Vector3d& voxel_center_imu,
                                         double voxel_size,
                                         const IMUST& current_pose,
                                         vector<pcl::PointXYZRGB>& points) {
    points.clear();
    
    // **检查体素是否有有效的平面参数**
    if(!voxel->plane.is_plane) return;
    
    // **获取平面参数（在世界坐标系中）**
    Eigen::Vector3d plane_normal = voxel->plane.normal;
    Eigen::Vector3d plane_center = voxel->plane.center;
    
    // **将平面参数转换到IMU坐标系**
    Eigen::Vector3d plane_normal_imu = current_pose.R.transpose() * plane_normal;
    Eigen::Vector3d plane_center_imu = current_pose.R.transpose() * (plane_center - current_pose.p);
    
    // **在体素内部生成平面采样点**
    double half_size = voxel_size * 0.5;  // 稍微小于体素尺寸，避免与边框重叠
    
    // **计算平面的两个切向量（垂直于法向量）**
    Eigen::Vector3d tangent1, tangent2;
    
    // 找一个与法向量不平行的向量
    if(abs(plane_normal_imu.x()) < 0.9) {
      tangent1 = Eigen::Vector3d(1, 0, 0).cross(plane_normal_imu).normalized();
    } else {
      tangent1 = Eigen::Vector3d(0, 1, 0).cross(plane_normal_imu).normalized();
    }
    tangent2 = plane_normal_imu.cross(tangent1).normalized();
    
    // **在平面上生成网格点**
    int grid_size = 20;  // 8x8网格
    for(int i = 0; i < grid_size; i++) {
      for(int j = 0; j < grid_size; j++) {
        double u = (i - grid_size/2.0) / grid_size * 2.0 * half_size;
        double v = (j - grid_size/2.0) / grid_size * 2.0 * half_size;
        
        // **计算平面上的点**
        Eigen::Vector3d plane_point = plane_center_imu + u * tangent1 + v * tangent2;
        
        // **检查点是否在体素内部**
        Eigen::Vector3d relative_pos = plane_point - voxel_center_imu;
        if(abs(relative_pos.x()) <= half_size && 
           abs(relative_pos.y()) <= half_size && 
           abs(relative_pos.z()) <= half_size) {
          
          pcl::PointXYZRGB p;
          p.x = plane_point.x();
          p.y = plane_point.y();
          p.z = plane_point.z();
          points.push_back(p);
        }
      }
    }
    
    // **添加平面中心点**
    if((plane_center_imu - voxel_center_imu).norm() <= half_size * 1.5) {
      pcl::PointXYZRGB center_point;
      center_point.x = plane_center_imu.x();
      center_point.y = plane_center_imu.y();
      center_point.z = plane_center_imu.z();
      points.push_back(center_point);
    }
  }
  
  // **生成未合并体素的个体平面可视化**
  void generate_individual_plane_visualization(OctoTree* voxel, 
                                             const Eigen::Vector3d& voxel_center_imu,
                                             double voxel_size,
                                             const IMUST& current_pose,
                                             vector<pcl::PointXYZRGB>& points) {
    points.clear();
    
    // **检查体素是否有有效的个体平面参数**
    if(!voxel->plane.is_plane) return;
    
    // **检查是否满足BA优化条件**
    if(voxel->layer < 0 || !voxel->isexist) return;
    
    // **检查平面性质量（与tras_opt中的条件一致）**
    if(voxel->eig_value.size() >= 2) {
      if(voxel->eig_value[0]/voxel->eig_value[1] > 0.12) {
        return;  // 平面性不够好，不会参与BA优化
      }
    }
    
    // **获取个体平面参数（在世界坐标系中）**
    Eigen::Vector3d plane_normal = voxel->plane.normal;
    Eigen::Vector3d plane_center = voxel->plane.center;
    
    // **将平面参数转换到IMU坐标系**
    Eigen::Vector3d plane_normal_imu = current_pose.R.transpose() * plane_normal;
    Eigen::Vector3d plane_center_imu = current_pose.R.transpose() * (plane_center - current_pose.p);
    
    // **在体素内部生成平面采样点（密度比合并平面稍低）**
    double half_size = voxel_size * 0.35;  // 稍微小一点，与合并平面区分
    
    // **计算平面的两个切向量（垂直于法向量）**
    Eigen::Vector3d tangent1, tangent2;
    
    // 找一个与法向量不平行的向量
    if(abs(plane_normal_imu.x()) < 0.9) {
      tangent1 = Eigen::Vector3d(1, 0, 0).cross(plane_normal_imu).normalized();
    } else {
      tangent1 = Eigen::Vector3d(0, 1, 0).cross(plane_normal_imu).normalized();
    }
    tangent2 = plane_normal_imu.cross(tangent1).normalized();
    
    // **在平面上生成网格点（6x6网格，比合并平面稍少）**
    int grid_size = 6;  // 6x6网格，36个点
    for(int i = 0; i < grid_size; i++) {
      for(int j = 0; j < grid_size; j++) {
        double u = (i - grid_size/2.0) / grid_size * 2.0 * half_size;
        double v = (j - grid_size/2.0) / grid_size * 2.0 * half_size;
        
        // **计算平面上的点**
        Eigen::Vector3d plane_point = plane_center_imu + u * tangent1 + v * tangent2;
        
        // **检查点是否在体素内部**
        Eigen::Vector3d relative_pos = plane_point - voxel_center_imu;
        if(abs(relative_pos.x()) <= half_size && 
           abs(relative_pos.y()) <= half_size && 
           abs(relative_pos.z()) <= half_size) {
          
          pcl::PointXYZRGB p;
          p.x = plane_point.x();
          p.y = plane_point.y();
          p.z = plane_point.z();
          points.push_back(p);
        }
      }
    }
    
    // **添加平面中心点（用十字形标记）**
    if((plane_center_imu - voxel_center_imu).norm() <= half_size * 1.2) {
      // 中心点
      pcl::PointXYZRGB center_point;
      center_point.x = plane_center_imu.x();
      center_point.y = plane_center_imu.y();
      center_point.z = plane_center_imu.z();
      points.push_back(center_point);
      
      // 添加十字形标记，突出显示个体平面中心
      double mark_size = voxel_size * 0.1;
      for(int axis = 0; axis < 3; axis++) {
        for(int dir = -1; dir <= 1; dir += 2) {
          Eigen::Vector3d mark_point = plane_center_imu;
          mark_point[axis] += dir * mark_size;
          
          pcl::PointXYZRGB mark;
          mark.x = mark_point.x();
          mark.y = mark_point.y();
          mark.z = mark_point.z();
          points.push_back(mark);
        }
      }
    }
  }
};

#endif // VOXEL_MERGE_VISUALIZER_HPP 