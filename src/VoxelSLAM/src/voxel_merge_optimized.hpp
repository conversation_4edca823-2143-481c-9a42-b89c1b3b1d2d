#ifndef VOXEL_MERGE_OPTIMIZED_HPP
#define VOXEL_MERGE_OPTIMIZED_HPP

#include "voxel_map.hpp"
#include "tools.hpp"
#include <unordered_set>
#include <unordered_map>
#include <vector>
#include <array>
#include <memory>

// **🚀 性能优化1：空间索引数据结构**
class SpatialVoxelIndex {
public:
    struct VoxelNode {
        VOXEL_LOC location;
        OctoTree* octree_ptr;
        int merge_group_id;
        int hierarchy_level;
        
        // **关键优化：预计算邻居指针，避免哈希查找**
        std::array<VoxelNode*, 6> direct_neighbors;  // 6个直接邻居
        std::vector<VoxelNode*> cross_level_neighbors;  // 跨层级邻居
        
        VoxelNode() : octree_ptr(nullptr), merge_group_id(-1), hierarchy_level(0) {
            direct_neighbors.fill(nullptr);
        }
    };

private:
    // **主索引：仍使用哈希表，但优化内存布局**
    std::unordered_map<VOXEL_LOC, std::unique_ptr<VoxelNode>> spatial_index;
    
    // **辅助索引：按层级分组，加速跨层级查找**
    std::array<std::vector<VoxelNode*>, 3> level_groups;  // 0-2层级
    
public:
    // **高效插入体素**
    VoxelNode* insert_voxel(const VOXEL_LOC& loc, OctoTree* octree) {
        auto node = std::make_unique<VoxelNode>();
        node->location = loc;
        node->octree_ptr = octree;
        node->hierarchy_level = octree ? octree->layer : 0;
        
        VoxelNode* node_ptr = node.get();
        spatial_index[loc] = std::move(node);
        level_groups[node_ptr->hierarchy_level].push_back(node_ptr);
        
        // **立即建立邻居关系**
        build_neighbor_connections(node_ptr);
        return node_ptr;
    }
    
    // **高效查找体素**
    VoxelNode* find_voxel(const VOXEL_LOC& loc) {
        auto it = spatial_index.find(loc);
        return (it != spatial_index.end()) ? it->second.get() : nullptr;
    }
    
    // **O(1)邻居访问**
    const std::array<VoxelNode*, 6>& get_direct_neighbors(VoxelNode* node) {
        return node->direct_neighbors;
    }
    
    // **批量获取候选邻居（核心优化）**
    void get_merge_candidates(VoxelNode* center, std::vector<VoxelNode*>& candidates) {
        candidates.clear();
        
        // 1. 添加直接邻居
        for(VoxelNode* neighbor : center->direct_neighbors) {
            if(neighbor && neighbor->octree_ptr && 
               neighbor->octree_ptr->octo_state == 0 && 
               neighbor->octree_ptr->plane.is_plane) {
                candidates.push_back(neighbor);
            }
        }
        
        // 2. 添加跨层级邻居
        for(VoxelNode* neighbor : center->cross_level_neighbors) {
            if(neighbor && neighbor->octree_ptr && 
               neighbor->octree_ptr->octo_state == 0 && 
               neighbor->octree_ptr->plane.is_plane) {
                candidates.push_back(neighbor);
            }
        }
    }

private:
    void build_neighbor_connections(VoxelNode* node) {
        const VOXEL_LOC& center = node->location;
        
        // **建立6个直接邻居连接**
        std::array<VOXEL_LOC, 6> neighbor_locs = {
            VOXEL_LOC{center.x+1, center.y, center.z},
            VOXEL_LOC{center.x-1, center.y, center.z},
            VOXEL_LOC{center.x, center.y+1, center.z},
            VOXEL_LOC{center.x, center.y-1, center.z},
            VOXEL_LOC{center.x, center.y, center.z+1},
            VOXEL_LOC{center.x, center.y, center.z-1}
        };
        
        for(int i = 0; i < 6; ++i) {
            VoxelNode* neighbor = find_voxel(neighbor_locs[i]);
            node->direct_neighbors[i] = neighbor;
            
            // **双向连接**
            if(neighbor) {
                neighbor->direct_neighbors[5-i] = node;  // 反向索引
            }
        }
        
        // **建立跨层级邻居连接**
        build_cross_level_connections(node);
    }
    
    void build_cross_level_connections(VoxelNode* node) {
        // **只对不同层级建立连接，减少计算量**
        for(int target_level = 0; target_level < 3; ++target_level) {
            if(target_level == node->hierarchy_level) continue;
            
            for(VoxelNode* candidate : level_groups[target_level]) {
                if(is_spatially_adjacent(node, candidate)) {
                    node->cross_level_neighbors.push_back(candidate);
                }
            }
        }
    }
    
    bool is_spatially_adjacent(VoxelNode* node1, VoxelNode* node2) {
        // **简化的空间邻接检查，避免复杂计算**
        const VOXEL_LOC& loc1 = node1->location;
        const VOXEL_LOC& loc2 = node2->location;
        int level1 = node1->hierarchy_level;
        int level2 = node2->hierarchy_level;
        
        // **快速距离检查**
        int64_t dx = abs(loc1.x - loc2.x);
        int64_t dy = abs(loc1.y - loc2.y);
        int64_t dz = abs(loc1.z - loc2.z);
        
        // **根据层级差异调整距离阈值**
        int64_t threshold = 1 << max(level1, level2);
        return (dx <= threshold && dy <= threshold && dz <= threshold);
    }
};

// **🚀 性能优化2：合并判断缓存**
class MergeDecisionCache {
private:
    // **使用更高效的键类型**
    struct VoxelPair {
        VOXEL_LOC loc1, loc2;
        
        VoxelPair(const VOXEL_LOC& l1, const VOXEL_LOC& l2) {
            if(l1 < l2) { loc1 = l1; loc2 = l2; }
            else { loc1 = l2; loc2 = l1; }
        }
        
        bool operator==(const VoxelPair& other) const {
            return loc1 == other.loc1 && loc2 == other.loc2;
        }
    };
    
    struct VoxelPairHash {
        size_t operator()(const VoxelPair& p) const {
            auto h1 = std::hash<VOXEL_LOC>{}(p.loc1);
            auto h2 = std::hash<VOXEL_LOC>{}(p.loc2);
            return h1 ^ (h2 << 1);
        }
    };
    
    std::unordered_map<VoxelPair, bool, VoxelPairHash> cache;
    size_t max_cache_size;
    
public:
    MergeDecisionCache(size_t max_size = 10000) : max_cache_size(max_size) {}
    
    bool get_cached_decision(const VOXEL_LOC& loc1, const VOXEL_LOC& loc2, bool& found) {
        VoxelPair key(loc1, loc2);
        auto it = cache.find(key);
        if(it != cache.end()) {
            found = true;
            return it->second;
        }
        found = false;
        return false;
    }
    
    void cache_decision(const VOXEL_LOC& loc1, const VOXEL_LOC& loc2, bool can_merge) {
        if(cache.size() >= max_cache_size) {
            cache.clear();  // **简单的清理策略**
        }
        
        VoxelPair key(loc1, loc2);
        cache[key] = can_merge;
    }
    
    void clear() { cache.clear(); }
    size_t size() const { return cache.size(); }
};

// **🚀 性能优化3：批量处理器**
class BatchMergeProcessor {
private:
    SpatialVoxelIndex* spatial_index;
    MergeDecisionCache merge_cache;
    
    struct MergeTask {
        SpatialVoxelIndex::VoxelNode* center;
        std::vector<SpatialVoxelIndex::VoxelNode*> candidates;
    };
    
public:
    BatchMergeProcessor(SpatialVoxelIndex* index) : spatial_index(index) {}
    
    // **批量处理合并候选**
    void process_merge_candidates(const std::vector<VOXEL_LOC>& updated_voxels,
                                const Eigen::Vector3d& radar_pos) {
        std::vector<MergeTask> merge_tasks;
        merge_tasks.reserve(updated_voxels.size());
        
        // **步骤1：批量准备合并任务**
        for(const VOXEL_LOC& loc : updated_voxels) {
            auto* center_node = spatial_index->find_voxel(loc);
            if(!center_node || !center_node->octree_ptr) continue;
            
            MergeTask task;
            task.center = center_node;
            spatial_index->get_merge_candidates(center_node, task.candidates);
            
            if(!task.candidates.empty()) {
                merge_tasks.push_back(std::move(task));
            }
        }
        
        // **步骤2：批量执行合并验证**
        for(MergeTask& task : merge_tasks) {
            process_single_merge_task(task, radar_pos);
        }
    }

private:
    void process_single_merge_task(MergeTask& task, const Eigen::Vector3d& radar_pos) {
        OctoTree* center_octree = task.center->octree_ptr;
        
        for(auto* candidate_node : task.candidates) {
            if(!candidate_node->octree_ptr) continue;
            
            // **缓存查找**
            bool found;
            bool cached_result = merge_cache.get_cached_decision(
                task.center->location, candidate_node->location, found);
            
            bool can_merge;
            if(found) {
                can_merge = cached_result;
            } else {
                // **执行昂贵的合并计算**
                can_merge = expensive_merge_calculation(center_octree, 
                                                      candidate_node->octree_ptr, 
                                                      radar_pos);
                // **缓存结果**
                merge_cache.cache_decision(task.center->location, 
                                         candidate_node->location, can_merge);
            }
            
            if(can_merge) {
                // **执行实际合并**
                execute_merge(task.center, candidate_node);
            }
        }
    }
    
    bool expensive_merge_calculation(OctoTree* voxel1, OctoTree* voxel2, 
                                   const Eigen::Vector3d& radar_pos) {
        // **这里调用原始的can_merge_voxels函数**
        // 但现在频率大大降低了
        // 原实现代码...
        return true;  // placeholder
    }
    
    void execute_merge(SpatialVoxelIndex::VoxelNode* node1, 
                      SpatialVoxelIndex::VoxelNode* node2) {
        // **执行实际的体素合并逻辑**
        // 更新合并组信息等...
    }
};

// **🚀 主优化管理器**
class OptimizedVoxelMergeManager {
private:
    SpatialVoxelIndex spatial_index;
    BatchMergeProcessor batch_processor;
    
    // **性能统计**
    struct PerformanceStats {
        size_t cache_hits = 0;
        size_t cache_misses = 0;
        size_t total_merges = 0;
        double avg_processing_time = 0.0;
    } stats;
    
public:
    OptimizedVoxelMergeManager() : batch_processor(&spatial_index) {}
    
    // **优化后的主处理接口**
    void perform_optimized_merging(const std::unordered_map<VOXEL_LOC, OctoTree*>& surf_map,
                                 const std::vector<VOXEL_LOC>& updated_voxels,
                                 const Eigen::Vector3d& radar_pos) {
        
        auto start_time = std::chrono::high_resolution_clock::now();
        
        // **步骤1：批量更新空间索引**
        update_spatial_index(surf_map, updated_voxels);
        
        // **步骤2：批量处理合并候选**
        batch_processor.process_merge_candidates(updated_voxels, radar_pos);
        
        auto end_time = std::chrono::high_resolution_clock::now();
        auto duration = std::chrono::duration_cast<std::chrono::microseconds>(end_time - start_time);
        
        stats.avg_processing_time = duration.count() / 1000.0;  // ms
    }
    
    // **性能报告**
    void print_performance_stats() {
        double cache_hit_rate = (double)stats.cache_hits / 
                               (stats.cache_hits + stats.cache_misses) * 100.0;
        
        std::cout << "=== 优化性能统计 ===" << std::endl;
        std::cout << "缓存命中率: " << cache_hit_rate << "%" << std::endl;
        std::cout << "平均处理时间: " << stats.avg_processing_time << "ms" << std::endl;
        std::cout << "总合并次数: " << stats.total_merges << std::endl;
    }

private:
    void update_spatial_index(const std::unordered_map<VOXEL_LOC, OctoTree*>& surf_map,
                            const std::vector<VOXEL_LOC>& updated_voxels) {
        for(const VOXEL_LOC& loc : updated_voxels) {
            auto it = surf_map.find(loc);
            if(it != surf_map.end() && it->second) {
                spatial_index.insert_voxel(loc, it->second);
            }
        }
    }
};

#endif // VOXEL_MERGE_OPTIMIZED_HPP 