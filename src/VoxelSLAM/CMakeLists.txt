cmake_minimum_required(VERSION 3.0.2)
project(voxel_slam)

# **🔧 Debug编译支持：改为RelWithDebInfo以包含调试符号**
set(CMAKE_BUILD_TYPE "RelWithDebInfo")

# **Debug编译选项设置**
set(CMAKE_CXX_FLAGS_DEBUG "-g3 -O0 -DDEBUG")
set(CMAKE_CXX_FLAGS_RELWITHDEBINFO "-g -O2 -DNDEBUG")

# 确保debug符号不被strip
set(CMAKE_STRIP FALSE)

# set(CMAKE_CXX_FLAGS "-std=c++14")
# set(CMAKE_CXX_FLAGS_RELEASE "-O3 -Wall -g")

ADD_COMPILE_OPTIONS(-std=c++14)
# **修改：保留debug符号的优化编译 + OpenMP支持**
set(CMAKE_CXX_FLAGS "-std=c++14 -O2 -g ${OpenMP_CXX_FLAGS}")

set(CMAKE_C_FLAGS "${CMAKE_C_FLAGS} -fexceptions ${OpenMP_C_FLAGS}" )
set(CMAKE_CXX_STANDARD 14)
set(CMAKE_CXX_STANDARD_REQUIRED ON)
set(CMAKE_CXX_EXTENSIONS OFF)
set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -std=c++14 -pthread -std=c++0x -std=c++14 -fexceptions ${OpenMP_CXX_FLAGS}")

find_package(catkin REQUIRED COMPONENTS
  roscpp
  rospy
  std_msgs
  pcl_conversions
  pcl_ros
  livox_ros_driver
)

# 可选依赖livox_ros_driver
find_package(livox_ros_driver QUIET)
if(livox_ros_driver_FOUND)
  message(STATUS "Found livox_ros_driver")
else()
  message(STATUS "livox_ros_driver not found, skipping...")
endif()

find_package(Eigen3 REQUIRED)
find_package(PCL REQUIRED)
find_package(GTSAM REQUIRED QUIET)
find_package(OpenMP REQUIRED)
find_package(PkgConfig QUIET)
if(PkgConfig_FOUND)
    pkg_check_modules(LIBUSB1 libusb-1.0)
endif()
# find_package(Ceres REQUIRED)

catkin_package(
  CATKIN_DEPENDS geometry_msgs nav_msgs roscpp rospy std_msgs 
  DEPENDS EIGEN3 PCL GTSAM
  INCLUDE_DIRS
)

include_directories(
  include
  ${catkin_INCLUDE_DIRS}
  ${PCL_INCLUDE_DIRS}
# ${CERES_INCLUDE_DIRS}
  ${GTSAM_INCLUDE_DIR}
)

add_executable(points_concat_filter src/points_concat_filter.cpp)
target_link_libraries(points_concat_filter 
  ${catkin_LIBRARIES} 
  ${PCL_LIBRARIES}
)

add_executable(voxelslam src/voxelslam.cpp src/BTC.cpp)

# 设置链接库列表
set(LINK_LIBS ${catkin_LIBRARIES} ${PCL_LIBRARIES} gtsam ${OpenMP_CXX_LIBRARIES})

# **🔧 修复libusb冲突：强制使用系统libusb**
# 找到正确的系统libusb库
find_library(SYSTEM_USB_LIBRARY 
    NAMES usb-1.0 
    PATHS /usr/lib/x86_64-linux-gnu 
    NO_DEFAULT_PATH
)

if(SYSTEM_USB_LIBRARY)
    message(STATUS "Found system libusb-1.0: ${SYSTEM_USB_LIBRARY}")
    # 强制链接到系统libusb，避免MVS SDK冲突
    list(APPEND LINK_LIBS ${SYSTEM_USB_LIBRARY})
    
    # 设置RPATH确保运行时使用正确的库
    set_target_properties(voxelslam PROPERTIES
        INSTALL_RPATH "/usr/lib/x86_64-linux-gnu"
        BUILD_WITH_INSTALL_RPATH TRUE
        INSTALL_RPATH_USE_LINK_PATH FALSE
    )
else()
    message(WARNING "System libusb-1.0 not found, may encounter runtime issues")
endif()

target_link_libraries(voxelslam ${LINK_LIBS})







