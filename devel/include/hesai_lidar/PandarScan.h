// Generated by gencpp from file hesai_lidar/PandarScan.msg
// DO NOT EDIT!


#ifndef HESAI_LIDAR_MESSAGE_PANDARSCAN_H
#define HESAI_LIDAR_MESSAGE_PANDARSCAN_H


#include <string>
#include <vector>
#include <memory>

#include <ros/types.h>
#include <ros/serialization.h>
#include <ros/builtin_message_traits.h>
#include <ros/message_operations.h>

#include <std_msgs/Header.h>
#include <hesai_lidar/PandarPacket.h>

namespace hesai_lidar
{
template <class ContainerAllocator>
struct PandarScan_
{
  typedef PandarScan_<ContainerAllocator> Type;

  PandarScan_()
    : header()
    , packets()  {
    }
  PandarScan_(const ContainerAllocator& _alloc)
    : header(_alloc)
    , packets(_alloc)  {
  (void)_alloc;
    }



   typedef  ::std_msgs::Header_<ContainerAllocator>  _header_type;
  _header_type header;

   typedef std::vector< ::hesai_lidar::PandarPacket_<ContainerAllocator> , typename std::allocator_traits<ContainerAllocator>::template rebind_alloc< ::hesai_lidar::PandarPacket_<ContainerAllocator> >> _packets_type;
  _packets_type packets;





  typedef boost::shared_ptr< ::hesai_lidar::PandarScan_<ContainerAllocator> > Ptr;
  typedef boost::shared_ptr< ::hesai_lidar::PandarScan_<ContainerAllocator> const> ConstPtr;

}; // struct PandarScan_

typedef ::hesai_lidar::PandarScan_<std::allocator<void> > PandarScan;

typedef boost::shared_ptr< ::hesai_lidar::PandarScan > PandarScanPtr;
typedef boost::shared_ptr< ::hesai_lidar::PandarScan const> PandarScanConstPtr;

// constants requiring out of line definition



template<typename ContainerAllocator>
std::ostream& operator<<(std::ostream& s, const ::hesai_lidar::PandarScan_<ContainerAllocator> & v)
{
ros::message_operations::Printer< ::hesai_lidar::PandarScan_<ContainerAllocator> >::stream(s, "", v);
return s;
}


template<typename ContainerAllocator1, typename ContainerAllocator2>
bool operator==(const ::hesai_lidar::PandarScan_<ContainerAllocator1> & lhs, const ::hesai_lidar::PandarScan_<ContainerAllocator2> & rhs)
{
  return lhs.header == rhs.header &&
    lhs.packets == rhs.packets;
}

template<typename ContainerAllocator1, typename ContainerAllocator2>
bool operator!=(const ::hesai_lidar::PandarScan_<ContainerAllocator1> & lhs, const ::hesai_lidar::PandarScan_<ContainerAllocator2> & rhs)
{
  return !(lhs == rhs);
}


} // namespace hesai_lidar

namespace ros
{
namespace message_traits
{





template <class ContainerAllocator>
struct IsMessage< ::hesai_lidar::PandarScan_<ContainerAllocator> >
  : TrueType
  { };

template <class ContainerAllocator>
struct IsMessage< ::hesai_lidar::PandarScan_<ContainerAllocator> const>
  : TrueType
  { };

template <class ContainerAllocator>
struct IsFixedSize< ::hesai_lidar::PandarScan_<ContainerAllocator> >
  : FalseType
  { };

template <class ContainerAllocator>
struct IsFixedSize< ::hesai_lidar::PandarScan_<ContainerAllocator> const>
  : FalseType
  { };

template <class ContainerAllocator>
struct HasHeader< ::hesai_lidar::PandarScan_<ContainerAllocator> >
  : TrueType
  { };

template <class ContainerAllocator>
struct HasHeader< ::hesai_lidar::PandarScan_<ContainerAllocator> const>
  : TrueType
  { };


template<class ContainerAllocator>
struct MD5Sum< ::hesai_lidar::PandarScan_<ContainerAllocator> >
{
  static const char* value()
  {
    return "70c3ed4f4ae9144323298b04cc2c760b";
  }

  static const char* value(const ::hesai_lidar::PandarScan_<ContainerAllocator>&) { return value(); }
  static const uint64_t static_value1 = 0x70c3ed4f4ae91443ULL;
  static const uint64_t static_value2 = 0x23298b04cc2c760bULL;
};

template<class ContainerAllocator>
struct DataType< ::hesai_lidar::PandarScan_<ContainerAllocator> >
{
  static const char* value()
  {
    return "hesai_lidar/PandarScan";
  }

  static const char* value(const ::hesai_lidar::PandarScan_<ContainerAllocator>&) { return value(); }
};

template<class ContainerAllocator>
struct Definition< ::hesai_lidar::PandarScan_<ContainerAllocator> >
{
  static const char* value()
  {
    return "Header header\n"
"PandarPacket[] packets\n"
"\n"
"================================================================================\n"
"MSG: std_msgs/Header\n"
"# Standard metadata for higher-level stamped data types.\n"
"# This is generally used to communicate timestamped data \n"
"# in a particular coordinate frame.\n"
"# \n"
"# sequence ID: consecutively increasing ID \n"
"uint32 seq\n"
"#Two-integer timestamp that is expressed as:\n"
"# * stamp.sec: seconds (stamp_secs) since epoch (in Python the variable is called 'secs')\n"
"# * stamp.nsec: nanoseconds since stamp_secs (in Python the variable is called 'nsecs')\n"
"# time-handling sugar is provided by the client library\n"
"time stamp\n"
"#Frame this data is associated with\n"
"string frame_id\n"
"\n"
"================================================================================\n"
"MSG: hesai_lidar/PandarPacket\n"
"# field		size(byte)\n"
"# SOB 		2\n"
"# angle		2\n"
"# measure	5\n"
"# block		SOB + angle + measure * 40\n"
"# timestamp	4\n"
"# factory	2\n"
"# reserve	8\n"
"# rpm		2\n"
"# tail		timestamp + factory + reserve + rpm\n"
"# packet	block * 6 + tail\n"
"\n"
"time stamp\n"
"uint8[] data\n"
"uint32 size\n"
;
  }

  static const char* value(const ::hesai_lidar::PandarScan_<ContainerAllocator>&) { return value(); }
};

} // namespace message_traits
} // namespace ros

namespace ros
{
namespace serialization
{

  template<class ContainerAllocator> struct Serializer< ::hesai_lidar::PandarScan_<ContainerAllocator> >
  {
    template<typename Stream, typename T> inline static void allInOne(Stream& stream, T m)
    {
      stream.next(m.header);
      stream.next(m.packets);
    }

    ROS_DECLARE_ALLINONE_SERIALIZER
  }; // struct PandarScan_

} // namespace serialization
} // namespace ros

namespace ros
{
namespace message_operations
{

template<class ContainerAllocator>
struct Printer< ::hesai_lidar::PandarScan_<ContainerAllocator> >
{
  template<typename Stream> static void stream(Stream& s, const std::string& indent, const ::hesai_lidar::PandarScan_<ContainerAllocator>& v)
  {
    s << indent << "header: ";
    s << std::endl;
    Printer< ::std_msgs::Header_<ContainerAllocator> >::stream(s, indent + "  ", v.header);
    s << indent << "packets[]" << std::endl;
    for (size_t i = 0; i < v.packets.size(); ++i)
    {
      s << indent << "  packets[" << i << "]: ";
      s << std::endl;
      s << indent;
      Printer< ::hesai_lidar::PandarPacket_<ContainerAllocator> >::stream(s, indent + "    ", v.packets[i]);
    }
  }
};

} // namespace message_operations
} // namespace ros

#endif // HESAI_LIDAR_MESSAGE_PANDARSCAN_H
