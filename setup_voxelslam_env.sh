#!/bin/bash

# VoxelSLAM 环境设置脚本 - 永久修复libusb冲突
# 用法：source setup_voxelslam_env.sh

echo "🔧 设置VoxelSLAM环境变量..."

# 1. 修复libusb库路径冲突
# 将系统libusb路径放在最前面，避免MVS SDK冲突
export LD_LIBRARY_PATH="/usr/lib/x86_64-linux-gnu:$LD_LIBRARY_PATH"

# 2. 移除可能冲突的MVS路径（如果存在）
export LD_LIBRARY_PATH=$(echo $LD_LIBRARY_PATH | sed 's|/opt/MVS/lib/64:||g')

# 3. 设置VoxelSLAM工作目录
VOXELSLAM_WS="/home/<USER>/ws_voxel_slam2"
if [ -d "$VOXELSLAM_WS" ]; then
    cd "$VOXELSLAM_WS"
    source devel/setup.bash
    echo "✅ VoxelSLAM workspace: $VOXELSLAM_WS"
fi

# 4. Phase 1优化环境变量
export VOXELSLAM_PHASE1_OPTIMIZATION=true
export VOXELSLAM_PERFORMANCE_LOGGING=true

echo "✅ VoxelSLAM环境设置完成！"
echo "   - libusb路径已修复"
echo "   - Phase 1优化已启用"
echo "   - 现在可以直接使用: roslaunch voxel_slam vxlm_hesai.launch"

# 验证libusb路径
if command -v ldd &> /dev/null; then
    echo "🔍 验证libusb路径:"
    if [ -f "$VOXELSLAM_WS/devel/lib/voxel_slam/voxelslam" ]; then
        ldd "$VOXELSLAM_WS/devel/lib/voxel_slam/voxelslam" | grep libusb || echo "   libusb依赖检查完成"
    fi
fi 