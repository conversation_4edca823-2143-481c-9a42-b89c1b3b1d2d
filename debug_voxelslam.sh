#!/bin/bash

# VoxelSLAM Debug 启动脚本
# 支持 gdb, valgrind, strace 等调试工具

echo "🔧 VoxelSLAM Debug Mode"

# 解析参数
DEBUG_TOOL=${1:-gdb}
RVIZ=${2:-true}

# 设置环境
export LD_LIBRARY_PATH="/usr/lib/x86_64-linux-gnu:$LD_LIBRARY_PATH"
cd $(dirname $0)
source devel/setup.bash

echo "🛠️  Debug工具: $DEBUG_TOOL"
echo "📊 RViz显示: $RVIZ"

case $DEBUG_TOOL in
    "gdb")
        echo "🚀 启动GDB调试模式..."
        roslaunch voxel_slam vxlm_hesai.launch debug:=true debug_tool:=gdb rviz:=$RVIZ
        ;;
    "valgrind")
        echo "🚀 启动Valgrind内存检查..."
        roslaunch voxel_slam vxlm_hesai.launch debug:=true debug_tool:=valgrind rviz:=$RVIZ
        ;;
    "strace")
        echo "🚀 启动Strace系统调用跟踪..."
        roslaunch voxel_slam vxlm_hesai.launch debug:=true debug_tool:=strace rviz:=$RVIZ
        echo "📝 Strace日志保存在: /tmp/voxelslam_strace.log"
        ;;
    "interactive")
        echo "🚀 启动交互式GDB模式..."
        echo "💡 使用方法："
        echo "   1. 在新终端运行: roslaunch voxel_slam vxlm_hesai.launch debug:=false"
        echo "   2. 找到voxelslam进程PID: ps aux | grep voxelslam"
        echo "   3. 附加GDB: gdb -p <PID>"
        ;;
    "core")
        echo "🚀 启动核心转储分析模式..."
        if [ -z "$2" ]; then
            echo "❌ 请提供core文件路径: $0 core <core_file_path>"
            exit 1
        fi
        CORE_FILE=$2
        echo "📁 分析core文件: $CORE_FILE"
        gdb devel/lib/voxel_slam/voxelslam $CORE_FILE
        ;;
    *)
        echo "❌ 未知的调试工具: $DEBUG_TOOL"
        echo ""
        echo "🛠️  支持的调试工具:"
        echo "   gdb        - GNU调试器"
        echo "   valgrind   - 内存泄漏检查"
        echo "   strace     - 系统调用跟踪"
        echo "   interactive - 交互式调试说明"
        echo "   core       - 核心转储分析"
        echo ""
        echo "📝 用法: $0 [debug_tool] [rviz=true/false]"
        echo "   例如: $0 gdb false"
        exit 1
        ;;
esac 